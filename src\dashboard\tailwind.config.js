/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./app/**/*.{js,ts,jsx,tsx}",
  ],
  safelist: [
    { pattern: /^grid-cols-/ },
    { pattern: /^text-.*-\d+/ },
    { pattern: /^bg-.*-\d+/ },
  ],
  theme: {
    extend: {
      backgroundImage: {
        'repeating-diagonal': 'repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.1) 10px, rgba(0,0,0,0.1) 20px)',
      },
      lineClamp: {
        3: '3',
      },
    },
  },
  plugins: [
    require('@tailwindcss/line-clamp'),
  ],
}; 