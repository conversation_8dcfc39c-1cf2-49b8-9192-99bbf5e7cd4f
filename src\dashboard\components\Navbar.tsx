// @ts-nocheck

import { signIn, signOut, useSession } from 'next-auth/react';
import Link from 'next/link';
import useGuildInfo from '../hooks/useGuildInfo';
import { useTheme } from '../contexts/ThemeContext';

export default function Navbar() {
  const { data: session } = useSession();
  const { displayName } = useGuildInfo();
  const { currentScheme } = useTheme();
  const headingText = displayName ? `${displayName} Dashboard` : 'Bot Dashboard';
  
  // Dynamic styling functions
  const getNavbarStyles = () => {
    return `
      px-4 sm:px-6 py-2 
      sticky top-0 z-50
      bg-white/5 backdrop-blur-xl 
      border-b border-white/20
      transition-colors duration-300
    `;
  };

  const getHeadingStyles = () => {
    return `
      text-xl font-bold 
      text-[${currentScheme.colors.text}]
    `;
  };

  const getSignInButtonStyles = (isSignOut = false) => {
    return `
      px-4 py-2 rounded 
      transition-all duration-300
      ${isSignOut 
        ? 'bg-red-500 text-white hover:bg-red-600' 
        : 'bg-blue-500 text-white hover:bg-blue-600'
      }
    `;
  };

  return (
    <nav className={getNavbarStyles()}>
      <div className="flex justify-between items-center max-w-7xl mx-auto">
        <h1 className={getHeadingStyles()}>{headingText}</h1>
        
        <div className="flex items-center space-x-4">
          {session ? (
            <div className="flex items-center space-x-4">
              {session.user?.image && (
                <img 
                  src={session.user.image} 
                  alt="User avatar" 
                  className="w-10 h-10 rounded-full object-cover border-2 border-white/20"
                />
              )}
              <button 
                onClick={() => signOut()} 
                className={getSignInButtonStyles(true)}
              >
                Sign Out
              </button>
            </div>
          ) : (
            <button 
              onClick={() => signIn()} 
              className={getSignInButtonStyles()}
            >
              Sign In
            </button>
          )}
        </div>
      </div>
    </nav>
  );
} 