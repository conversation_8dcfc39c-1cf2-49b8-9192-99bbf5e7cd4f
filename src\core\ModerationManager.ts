import { Client, Guild, GuildMember, Message, TextChannel, User, EmbedBuilder, GuildChannel } from 'discord.js';
import { DatabaseManager } from './DatabaseManager.js';
import winston from 'winston';

interface BypassRule {
  id: string;
  name: string;
  description: string;
  roleIds: string[];
  bypassFeatures: string[];
  priority: number;
  enabled: boolean;
}

interface ModerationConfig {
  moderation: {
    raidProtection: {
      enabled: boolean;
      joinThreshold: number;
      timeWindowMs: number;
      mitigationDurationMs: number;
      mitigationAction: string;
      whitelistedRoles: string[];
      notifyChannel: string | null;
      dmUser: boolean;
      embedNotification: {
        enabled: boolean;
        title: string;
        description: string;
        color: string;
        footer: string;
      };
      /** Usernames matching any of these patterns trigger immediate mitigation */
      usernameRegexPatterns?: { pattern: string; flags?: string }[];
    };
    spamPrevention: {
      enabled: boolean;
      msgThreshold: number;
      timeWindowMs: number;
      timeoutMs: number;
      action: string;
      whitelistedRoles: string[];
      whitelistedChannels: string[];
      /** Messages matching any of these regex patterns are treated as spam instantly */
      regexPatterns?: { pattern: string; flags?: string }[];
      deleteMessages: boolean;
      warnUser: boolean;
      dmUser: boolean;
      escalation: {
        enabled: boolean;
        secondOffenseAction: string;
        thirdOffenseAction: string;
        severeCaseAction: string;
        resetAfterMs: number;
      };
      embedNotification: {
        enabled: boolean;
        title: string;
        description: string;
        color: string;
        footer: string;
      };
    };
    logging: {
      enabled: boolean;
      channelId: string;
      logRaidProtection: boolean;
      logSpamPrevention: boolean;
      logAutoMod: boolean;
      logManualActions: boolean;
      logWarnings: boolean;
      logTimeouts: boolean;
      logKicks: boolean;
      logBans: boolean;
      embedLogs: boolean;
      detailedLogs: boolean;
    };
    bypass: {
      enabled: boolean;
      rules: BypassRule[];
      inheritanceMode: 'highest' | 'additive';
      logBypassEvents: boolean;
    };
    autoMod?: {
      enabled: boolean;
      /** Generic regex filters with advanced fields */
      regexFilters?: {
        name?: string;
        description?: string;
        pattern: string;
        flags?: string;
        action: string; // delete | filter
        deleteMessage?: boolean;
        punishment?: string; // warn | timeout | kick | ban
        timeoutDuration?: number;
        whitelist?: string[];
        notifyMods?: boolean;
      }[];
      profanityFilter?: {
        enabled: boolean;
        customWords: string[];
        action: string; // warn | timeout | kick | ban
        deleteMessage: boolean;
      };
      linkFilter?: {
        enabled: boolean;
        blockedDomains: string[];
        allowedDomains?: string[];
        action: string; // warn | timeout | kick | ban
        deleteMessage: boolean;
      };
      capsFilter?: {
        enabled: boolean;
        minLength: number;
        threshold: number;
        action: string; // warn | timeout | kick | ban
        deleteMessage: boolean;
      };
      mentionSpam?: {
        enabled: boolean;
        threshold: number;
        action: string; // warn | timeout | kick | ban
        timeoutDuration?: number;
        deleteMessage: boolean;
      };
      duplicateMessages?: {
        enabled: boolean;
        threshold: number;
        timeWindowMs: number;
        action: string; // warn | timeout | kick | ban
        deleteMessage: boolean;
      };
    };
  };
}

interface RaidTracker {
  joins: { userId: string; timestamp: number }[];
  mitigationActive: boolean;
  mitigationEnds: number;
}

interface SpamTracker {
  [userId: string]: {
    messages: { timestamp: number; messageId: string }[];
    offenses: number;
    lastReset: number;
  };
}

export class ModerationManager {
  private client: Client;
  private database: DatabaseManager;
  private logger: winston.Logger;
  private raidTrackers: Map<string, RaidTracker> = new Map();
  private spamTrackers: Map<string, SpamTracker> = new Map();
  private autoModDuplicateTrackers: Map<string, { lastMessage: string; count: number; firstTimestamp: number }> = new Map();

  constructor(client: Client, database: DatabaseManager, logger: winston.Logger) {
    this.client = client;
    this.database = database;
    this.logger = logger;
    this.initializeEventListeners();
  }

  private initializeEventListeners(): void {
    // Member join event for raid protection
    this.client.on('guildMemberAdd', async (member) => {
      await this.handleRaidProtection(member);
    });

    // Message event for spam prevention
    this.client.on('messageCreate', async (message) => {
      if (message.author.bot || !message.guild) return;
      await this.handleSpamPrevention(message);
      await this.handleAutoMod(message);
    });
  }

  private async getGuildConfig(guildId: string): Promise<ModerationConfig | null> {
    try {
      const config = await this.database.findOne('guild_configs', { guildId });
      return config as ModerationConfig;
    } catch (error) {
      this.logger.error(`Failed to get moderation config for guild ${guildId}:`, error);
      return null;
    }
  }

  private async checkBypass(member: GuildMember, feature: string, config: ModerationConfig): Promise<boolean> {
    if (!config.moderation.bypass?.enabled) return false;

    const bypassRules = config.moderation.bypass.rules
      .filter(rule => rule.enabled && rule.bypassFeatures.includes(feature))
      .sort((a, b) => b.priority - a.priority); // Sort by priority (highest first)

    const applicableRules: BypassRule[] = [];

    // Find all rules that apply to this member
    for (const rule of bypassRules) {
      const hasRequiredRole = rule.roleIds.some(roleId => 
        member.roles.cache.has(roleId)
      );
      if (hasRequiredRole) {
        applicableRules.push(rule);
      }
    }

    if (applicableRules.length === 0) return false;

    // Handle inheritance mode
    if (config.moderation.bypass.inheritanceMode === 'highest') {
      // Use the highest priority rule only
      const highestPriorityRule = applicableRules[0];
      if (config.moderation.bypass.logBypassEvents) {
        await this.logBypassEvent(member, feature, highestPriorityRule);
      }
      return true;
    } else {
      // Additive mode - if any rule applies, grant bypass
      if (config.moderation.bypass.logBypassEvents) {
        await this.logBypassEvent(member, feature, applicableRules[0]);
      }
      return true;
    }
  }

  private async logBypassEvent(member: GuildMember, feature: string, rule: BypassRule): Promise<void> {
    try {
      await this.logModerationAction(member.guild, {
        action: 'Bypass Applied',
        details: `User ${member.user.tag} bypassed ${feature} due to rule: ${rule.name} (Priority: ${rule.priority})`,
        moderator: this.client.user!,
        targetUser: member.user,
        config: { enabled: true, embedLogs: true, detailedLogs: true }
      });
    } catch (error) {
      this.logger.error(`Failed to log bypass event:`, error);
    }
  }

  private async handleRaidProtection(member: GuildMember): Promise<void> {
    const config = await this.getGuildConfig(member.guild.id);
    if (!config?.moderation?.raidProtection?.enabled) return;

    // Check if user has bypass for raid protection
    const hasBypass = await this.checkBypass(member, 'raidProtection', config);
    if (hasBypass) return;

    const raidConfig = config.moderation.raidProtection;

    // Username regex detection – immediate mitigation
    if (raidConfig.usernameRegexPatterns && raidConfig.usernameRegexPatterns.length > 0) {
      for (const { pattern, flags } of raidConfig.usernameRegexPatterns) {
        try {
          const regex = new RegExp(pattern, flags ?? undefined);
          if (regex.test(member.user.username)) {
            await this.executeRaidMitigation(member, raidConfig, 'username pattern matched');
            return; // Already mitigated
          }
        } catch (err) {
          this.logger.warn(`Invalid username regex pattern in raid protection: ${pattern}`, err);
        }
      }
    }

    const guildId = member.guild.id;
    const now = Date.now();

    // Initialize raid tracker if not exists
    if (!this.raidTrackers.has(guildId)) {
      this.raidTrackers.set(guildId, {
        joins: [],
        mitigationActive: false,
        mitigationEnds: 0
      });
    }

    const tracker = this.raidTrackers.get(guildId)!;

    // Check if mitigation is still active
    if (tracker.mitigationActive && now < tracker.mitigationEnds) {
      await this.executeRaidMitigation(member, raidConfig, 'raid mitigation active');
      return;
    } else if (tracker.mitigationActive && now >= tracker.mitigationEnds) {
      // Mitigation period ended
      tracker.mitigationActive = false;
      tracker.joins = [];
    }

    // Add current join to tracker
    tracker.joins.push({ userId: member.id, timestamp: now });

    // Remove old joins outside time window
    tracker.joins = tracker.joins.filter(join => 
      now - join.timestamp <= raidConfig.timeWindowMs
    );

    // Check if threshold exceeded
    if (tracker.joins.length >= raidConfig.joinThreshold) {
      // Activate raid mitigation
      tracker.mitigationActive = true;
      tracker.mitigationEnds = now + raidConfig.mitigationDurationMs;

      // Execute mitigation on recent joiners
      const recentJoiners = tracker.joins.slice(-raidConfig.joinThreshold);
      for (const joiner of recentJoiners) {
        const targetMember = await member.guild.members.fetch(joiner.userId).catch(() => null);
        if (targetMember) {
          await this.executeRaidMitigation(targetMember, raidConfig, 'raid protection triggered');
        }
      }

      // Log raid protection activation
      await this.logModerationAction(member.guild, {
        action: 'Raid Protection Activated',
        details: `Threshold exceeded: ${tracker.joins.length} joins in ${raidConfig.timeWindowMs}ms`,
        moderator: this.client.user!,
        config: config.moderation.logging
      });
    }
  }

  private async executeRaidMitigation(member: GuildMember, raidConfig: any, reason: string): Promise<void> {
    try {
      // Get config for bypass checking
      const config = await this.getGuildConfig(member.guild.id);
      if (config) {
        // Check if user has bypass for raid protection
        const hasBypass = await this.checkBypass(member, 'raidProtection', config);
        if (hasBypass) return;
      }

      // Check if user has whitelisted roles
      const hasWhitelistedRole = member.roles.cache.some(role => 
        raidConfig.whitelistedRoles.includes(role.id)
      );
      if (hasWhitelistedRole) return;

      // Execute mitigation action
      switch (raidConfig.mitigationAction) {
        case 'ban':
          await member.ban({ reason });
          break;
        case 'kick':
          await member.kick(reason);
          break;
        case 'timeout':
          await member.timeout(raidConfig.mitigationDurationMs, reason);
          break;
      }

      // Send DM if enabled
      if (raidConfig.dmUser && raidConfig.embedNotification.enabled) {
        await this.sendModerationDM(member.user, member.guild, {
          title: raidConfig.embedNotification.title,
          description: raidConfig.embedNotification.description,
          color: raidConfig.embedNotification.color,
          footer: raidConfig.embedNotification.footer,
          action: raidConfig.mitigationAction,
          reason,
          threshold: raidConfig.joinThreshold,
          timeWindow: raidConfig.timeWindowMs
        });
      }

      // Send notification to channel
      if (raidConfig.notifyChannel) {
        const channel = await member.guild.channels.fetch(raidConfig.notifyChannel).catch(() => null);
        if (channel && channel instanceof TextChannel) {
          await this.sendModerationNotification(channel, member.user, member.guild, {
            title: raidConfig.embedNotification.title,
            description: raidConfig.embedNotification.description,
            color: raidConfig.embedNotification.color,
            footer: raidConfig.embedNotification.footer,
            action: raidConfig.mitigationAction,
            reason,
            threshold: raidConfig.joinThreshold,
            timeWindow: raidConfig.timeWindowMs
          });
        }
      }

    } catch (error) {
      this.logger.error(`Failed to execute raid mitigation for ${member.user.tag}:`, error);
    }
  }

  private async handleSpamPrevention(message: Message): Promise<void> {
    const config = await this.getGuildConfig(message.guild!.id);
    if (!config?.moderation?.spamPrevention?.enabled) return;

    const spamConfig = config.moderation.spamPrevention;
    const guildId = message.guild!.id;
    const userId = message.author.id;
    const now = Date.now();

    // Check if user has whitelisted roles or bypass
    const member = await message.guild!.members.fetch(userId).catch(() => null);
    if (member) {
      // Check if user has bypass for spam prevention
      const hasBypass = await this.checkBypass(member, 'spamPrevention', config);
      if (hasBypass) return;

      const hasWhitelistedRole = member.roles.cache.some(role => 
        spamConfig.whitelistedRoles.includes(role.id)
      );
      if (hasWhitelistedRole) return;
    }

    // Regex pattern instant spam detection
    if (spamConfig.regexPatterns && spamConfig.regexPatterns.length > 0) {
      for (const { pattern, flags } of spamConfig.regexPatterns) {
        try {
          const regex = new RegExp(pattern, flags ?? undefined);
          if (regex.test(message.content)) {
            const tempTracker = {
              messages: [{ timestamp: now, messageId: message.id }],
              offenses: 0,
              lastReset: now
            };
            await this.executeSpamPrevention(message, spamConfig, tempTracker);
            return;
          }
        } catch (err) {
          this.logger.warn(`Invalid spam regex pattern: ${pattern}`, err);
        }
      }
    }

    // Check if channel is whitelisted
    if (spamConfig.whitelistedChannels.includes(message.channel.id)) return;

    // Initialize spam tracker for guild if not exists
    if (!this.spamTrackers.has(guildId)) {
      this.spamTrackers.set(guildId, {});
    }

    const guildTracker = this.spamTrackers.get(guildId)!;

    // Initialize user tracker if not exists
    if (!guildTracker[userId]) {
      guildTracker[userId] = {
        messages: [],
        offenses: 0,
        lastReset: now
      };
    }

    const userTracker = guildTracker[userId];

    // Reset offenses if enough time has passed
    if (spamConfig.escalation.enabled && now - userTracker.lastReset > spamConfig.escalation.resetAfterMs) {
      userTracker.offenses = 0;
      userTracker.lastReset = now;
    }

    // Add current message to tracker
    userTracker.messages.push({ timestamp: now, messageId: message.id });

    // Remove old messages outside time window
    userTracker.messages = userTracker.messages.filter(msg => 
      now - msg.timestamp <= spamConfig.timeWindowMs
    );

    // Check if threshold exceeded
    if (userTracker.messages.length >= spamConfig.msgThreshold) {
      await this.executeSpamPrevention(message, spamConfig, userTracker);
    }
  }

  private async executeSpamPrevention(message: Message, spamConfig: any, userTracker: any): Promise<void> {
    try {
      const member = await message.guild!.members.fetch(message.author.id).catch(() => null);
      if (!member) return;

      // Increment offense count
      userTracker.offenses++;

      // Determine action based on escalation
      let action = spamConfig.action;
      if (spamConfig.escalation.enabled) {
        if (userTracker.offenses >= 3) {
          action = spamConfig.escalation.severeCaseAction;
        } else if (userTracker.offenses === 2) {
          action = spamConfig.escalation.thirdOffenseAction;
        } else if (userTracker.offenses === 1) {
          action = spamConfig.escalation.secondOffenseAction;
        }
      }

      // Delete messages if enabled
      if (spamConfig.deleteMessages) {
        for (const msg of userTracker.messages) {
          try {
            const messageToDelete = await message.channel.messages.fetch(msg.messageId);
            await messageToDelete.delete();
          } catch (error) {
            // Message might already be deleted
          }
        }
      }

      // Execute action
      const reason = `Spam prevention: ${userTracker.messages.length} messages in ${spamConfig.timeWindowMs}ms`;
      switch (action) {
        case 'ban':
          await member.ban({ reason });
          break;
        case 'kick':
          await member.kick(reason);
          break;
        case 'timeout':
          await member.timeout(spamConfig.timeoutMs, reason);
          break;
        case 'warn':
          // Warning system would be implemented here
          break;
      }

      // Send DM if enabled
      if (spamConfig.dmUser && spamConfig.embedNotification.enabled) {
        await this.sendModerationDM(message.author, message.guild!, {
          title: spamConfig.embedNotification.title,
          description: spamConfig.embedNotification.description,
          color: spamConfig.embedNotification.color,
          footer: spamConfig.embedNotification.footer,
          action,
          reason,
          messageCount: userTracker.messages.length,
          timeWindow: spamConfig.timeWindowMs,
          channel: message.channel as GuildChannel
        });
      }

      // Log spam prevention action
      await this.logModerationAction(message.guild!, {
        action: 'Spam Prevention',
        details: `User: ${message.author.tag}, Action: ${action}, Messages: ${userTracker.messages.length}`,
        moderator: this.client.user!,
        targetUser: message.author,
        config: (await this.getGuildConfig(message.guild!.id))?.moderation?.logging
      });

      // Clear user's message tracker
      userTracker.messages = [];

    } catch (error) {
      this.logger.error(`Failed to execute spam prevention for ${message.author.tag}:`, error);
    }
  }

  private async handleAutoMod(message: Message): Promise<void> {
    const config = await this.getGuildConfig(message.guild!.id);
    if (!config?.moderation?.autoMod?.enabled) return;

    const autoConfig: any = config.moderation.autoMod;

    // bypass check
    const member = await message.guild!.members.fetch(message.author.id).catch(() => null);
    if (member) {
      const hasBypass = await this.checkBypass(member, 'autoMod', config);
      if (hasBypass) return;
    }

    // Helper to perform punishment
    const punish = async (action: string, reason: string, timeoutMs?: number) => {
      if (!member) return;
      switch (action) {
        case 'warn':
          // could integrate warnings system; for now send DM
          await this.sendModerationDM(member.user, message.guild!, {
            title: '⚠️ Warning',
            description: reason,
            color: '#FFAA00',
            footer: 'Auto-Moderation Warning'
          }).catch(() => {});
          break;
        case 'timeout':
          await member.timeout(timeoutMs || autoConfig.mentionSpam?.timeoutDuration || 600000, reason).catch(() => {});
          break;
        case 'kick':
          await member.kick(reason).catch(() => {});
          break;
        case 'ban':
          await member.ban({ reason }).catch(() => {});
          break;
      }
    };

    // Regex Filters
    if (autoConfig.regexFilters && Array.isArray(autoConfig.regexFilters)) {
      for (const filter of autoConfig.regexFilters) {
        try {
          const regex = new RegExp(filter.pattern, filter.flags || undefined);
          if (regex.test(message.content)) {
            if (filter.action === 'delete') {
              await message.delete().catch(() => {});
            }
            if (filter.punishment) {
              await punish(filter.punishment, `[RegexFilter:${filter.name}]`, filter.timeoutDuration);
            }
            return; // stop after first match
          }
        } catch (err) {
          this.logger.warn(`Invalid regexFilter pattern: ${filter.pattern}`);
        }
      }
    }

    // Profanity Filter using customWords list simple match
    if (autoConfig.profanityFilter?.enabled) {
      const words = autoConfig.profanityFilter.customWords || [];
      if (words.length) {
        const pattern = new RegExp(`\\b(${words.join('|')})\\b`, 'i');
        if (pattern.test(message.content)) {
          if (autoConfig.profanityFilter.deleteMessage) await message.delete().catch(() => {});
          await punish(autoConfig.profanityFilter.action, 'Profanity Detected');
          return;
        }
      }
    }

    // Link Filter
    if (autoConfig.linkFilter?.enabled) {
      const urlRegex = /(https?:\/\/[^\s]+)/gi;
      const matches = message.content.match(urlRegex);
      if (matches) {
        for (const link of matches) {
          const domain = link.replace('http://', '').replace('https://', '').split('/')[0].toLowerCase();
          if (autoConfig.linkFilter.blockedDomains?.includes(domain)) {
            if (autoConfig.linkFilter.deleteMessage) await message.delete().catch(() => {});
            await punish(autoConfig.linkFilter.action, 'Blocked Link Detected');
            return;
          }
          if (autoConfig.linkFilter.allowedDomains && autoConfig.linkFilter.allowedDomains.length) {
            if (!autoConfig.linkFilter.allowedDomains.includes(domain)) {
              if (autoConfig.linkFilter.deleteMessage) await message.delete().catch(() => {});
              await punish(autoConfig.linkFilter.action, 'Disallowed Link');
              return;
            }
          }
        }
      }
    }

    // Caps Filter
    if (autoConfig.capsFilter?.enabled) {
      const content = message.content;
      if (content.length >= autoConfig.capsFilter.minLength) {
        const upperCount = content.replace(/[^A-Z]/g, '').length;
        const percent = (upperCount / content.length) * 100;
        if (percent >= autoConfig.capsFilter.threshold) {
          if (autoConfig.capsFilter.deleteMessage) await message.delete().catch(() => {});
          await punish(autoConfig.capsFilter.action, 'Excessive Caps');
          return;
        }
      }
    }

    // Mention Spam
    if (autoConfig.mentionSpam?.enabled) {
      const mentionCount = message.mentions.users.size;
      if (mentionCount >= autoConfig.mentionSpam.threshold) {
        if (autoConfig.mentionSpam.deleteMessage) await message.delete().catch(() => {});
        await punish(autoConfig.mentionSpam.action, 'Mass Mention', autoConfig.mentionSpam.timeoutDuration);
        return;
      }
    }

    // Duplicate Messages
    if (autoConfig.duplicateMessages?.enabled) {
      const key = `${message.guild!.id}:${message.author.id}`;
      const tracker = this.autoModDuplicateTrackers.get(key) || { lastMessage: '', count: 0, firstTimestamp: Date.now() };
      if (message.content === tracker.lastMessage) {
        tracker.count += 1;
      } else {
        tracker.lastMessage = message.content;
        tracker.count = 1;
        tracker.firstTimestamp = Date.now();
      }
      this.autoModDuplicateTrackers.set(key, tracker);
      // cleanse after timeWindow
      if (Date.now() - tracker.firstTimestamp > autoConfig.duplicateMessages.timeWindowMs) {
        tracker.count = 1;
        tracker.firstTimestamp = Date.now();
      }
      if (tracker.count >= autoConfig.duplicateMessages.threshold) {
        if (autoConfig.duplicateMessages.deleteMessage) await message.delete().catch(() => {});
        await punish(autoConfig.duplicateMessages.action, 'Duplicate Messages');
        tracker.count = 0; // reset
        return;
      }
    }
  }

  private async sendModerationDM(user: User, guild: Guild, options: any): Promise<void> {
    try {
      const embed = new EmbedBuilder()
        .setTitle(this.replaceModerationPlaceholders(options.title, { user, guild, ...options }))
        .setDescription(this.replaceModerationPlaceholders(options.description, { user, guild, ...options }))
        .setColor(options.color)
        .setFooter({ text: this.replaceModerationPlaceholders(options.footer, { user, guild, ...options }) })
        .setTimestamp();

      await user.send({ embeds: [embed] });
    } catch (error) {
      this.logger.warn(`Failed to send DM to ${user.tag}:`, error);
    }
  }

  private async sendModerationNotification(channel: TextChannel, user: User, guild: Guild, options: any): Promise<void> {
    try {
      const embed = new EmbedBuilder()
        .setTitle(this.replaceModerationPlaceholders(options.title, { user, guild, ...options }))
        .setDescription(this.replaceModerationPlaceholders(options.description, { user, guild, ...options }))
        .setColor(options.color)
        .setFooter({ text: this.replaceModerationPlaceholders(options.footer, { user, guild, ...options }) })
        .setTimestamp();

      await channel.send({ embeds: [embed] });
    } catch (error) {
      this.logger.warn(`Failed to send notification to channel ${channel.name}:`, error);
    }
  }

  private async logModerationAction(guild: Guild, options: {
    action: string;
    details: string;
    moderator: User;
    targetUser?: User;
    config?: any;
  }): Promise<void> {
    if (!options.config?.enabled || !options.config?.channelId) return;

    try {
      const channel = await guild.channels.fetch(options.config.channelId).catch(() => null);
      if (!channel || !(channel instanceof TextChannel)) return;

      const embed = new EmbedBuilder()
        .setTitle(`🛡️ ${options.action}`)
        .setDescription(options.details)
        .setColor('#FF6B35')
        .setFooter({ text: `Moderator: ${options.moderator.tag}` })
        .setTimestamp();

      if (options.targetUser) {
        embed.addFields({
          name: 'Target User',
          value: `${options.targetUser.tag} (${options.targetUser.id})`,
          inline: true
        });
      }

      await channel.send({ embeds: [embed] });
    } catch (error) {
      this.logger.warn(`Failed to log moderation action:`, error);
    }
  }

  private replaceModerationPlaceholders(text: string, options: {
    user?: User;
    guild?: Guild;
    moderator?: User;
    reason?: string;
    action?: string;
    channel?: GuildChannel;
    duration?: number;
    expiresAt?: Date;
    threshold?: number;
    timeWindow?: number;
    messageCount?: number;
    warningCount?: number;
    maxWarnings?: number;
    timestamp?: Date;
  }): string {
    if (!text) return '';
    
    const {
      user,
      guild,
      moderator,
      reason = 'No reason provided',
      action = 'Unknown action',
      channel,
      duration,
      expiresAt,
      threshold,
      timeWindow,
      messageCount,
      warningCount,
      maxWarnings,
      timestamp = new Date()
    } = options;

    let result = text;

    // User placeholders
    if (user) {
      result = result
        .replace(/\{user\}/g, user.toString())
        .replace(/\{username\}/g, user.username)
        .replace(/\{userName\}/g, user.username)
        .replace(/\{userTag\}/g, user.tag)
        .replace(/\{user\.tag\}/g, user.tag)
        .replace(/\{user\.mention\}/g, user.toString())
        .replace(/\{userId\}/g, user.id)
        .replace(/\{user\.id\}/g, user.id)
        .replace(/\{userBanner\}/g, user.bannerURL({ size: 1024 }) || 'No Banner')
        .replace(/\{UserCreation\}/g, `<t:${Math.floor(user.createdTimestamp / 1000)}:R>`)
        .replace(/\{user-createdAt\}/g, `<t:${Math.floor(user.createdTimestamp / 1000)}:F>`);
    }

    // Guild placeholders
    if (guild) {
      result = result
        .replace(/\{guild\}/g, guild.name)
        .replace(/\{guild\.name\}/g, guild.name)
        .replace(/\{server\}/g, guild.name)
        .replace(/\{guildName\}/g, guild.name)
        .replace(/\{guildIcon\}/g, guild.iconURL() || '')
        .replace(/\{memberCount\}/g, this.toOrdinal(guild.memberCount))
        .replace(/\{member\.count\}/g, this.toOrdinal(guild.memberCount))
        .replace(/\{memberCountNumeric\}/g, guild.memberCount.toString());
    }

    // Moderator placeholders
    if (moderator) {
      result = result
        .replace(/\{moderator\}/g, moderator.toString())
        .replace(/\{moderatorName\}/g, moderator.username)
        .replace(/\{moderator\.name\}/g, moderator.username)
        .replace(/\{moderator\.tag\}/g, moderator.tag)
        .replace(/\{moderator\.mention\}/g, moderator.toString());
    }

    // Action and reason placeholders
    result = result
      .replace(/\{action\}/g, action)
      .replace(/\{reason\}/g, reason);

    // Channel placeholders
    if (channel) {
      result = result
        .replace(/\{channel\}/g, channel.toString())
        .replace(/\{channelName\}/g, channel.name)
        .replace(/\{channel\.name\}/g, channel.name)
        .replace(/\{channel\.mention\}/g, channel.toString());
    }

    // Duration and time placeholders
    if (duration !== undefined) {
      const durationText = this.formatDuration(duration);
      result = result.replace(/\{duration\}/g, durationText);
    }

    if (expiresAt) {
      result = result
        .replace(/\{expiresAt\}/g, `<t:${Math.floor(expiresAt.getTime() / 1000)}:F>`)
        .replace(/\{expiresAtRelative\}/g, `<t:${Math.floor(expiresAt.getTime() / 1000)}:R>`);
    }

    // Threshold and count placeholders
    if (threshold !== undefined) {
      result = result.replace(/\{threshold\}/g, threshold.toString());
    }

    if (timeWindow !== undefined) {
      result = result.replace(/\{timeWindow\}/g, timeWindow.toString());
    }

    if (messageCount !== undefined) {
      result = result.replace(/\{messageCount\}/g, messageCount.toString());
    }

    if (warningCount !== undefined) {
      result = result.replace(/\{warningCount\}/g, warningCount.toString());
    }

    if (maxWarnings !== undefined) {
      result = result.replace(/\{maxWarnings\}/g, maxWarnings.toString());
    }

    // Time placeholders
    result = result
      .replace(/\{timestamp\}/g, `<t:${Math.floor(timestamp.getTime() / 1000)}:F>`)
      .replace(/\{timestampRelative\}/g, `<t:${Math.floor(timestamp.getTime() / 1000)}:R>`)
      .replace(/\{longTime\}/g, `<t:${Math.floor(timestamp.getTime() / 1000)}:F>`)
      .replace(/\{shortTime\}/g, `<t:${Math.floor(timestamp.getTime() / 1000)}:T>`);

    return result;
  }

  private toOrdinal(num: number): string {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = num % 100;
    return num + (s[(v - 20) % 10] || s[v] || s[0] || 'th');
  }

  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days !== 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''}`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    } else {
      return `${seconds} second${seconds !== 1 ? 's' : ''}`;
    }
  }
} 