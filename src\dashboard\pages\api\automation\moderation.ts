// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { getDb } from '../../../apiHelpers/db';
import { dashboardConfig } from '../../../core/config';

const defaultSettings = {
  moderation: {
    raidProtection: {
      enabled: true,
      joinThreshold: 5,
      timeWindowMs: 10000,
      mitigationDurationMs: 300000,
      mitigationAction: 'ban',
      whitelistedRoles: [], // Roles exempt from raid protection
      notifyChannel: null,
      dmUser: true,
      usernameRegexPatterns: [
        // { pattern: "spam\d+", flags: "i" }
      ], // Usernames that trigger immediate mitigation
      embedNotification: {
        enabled: true,
        title: '🛡️ Raid Protection Triggered',
        description: 'User {user} was {action} due to raid protection.\n\n**Details:**\n• Join threshold exceeded: {threshold} users in {timeWindow}ms\n• Action taken: {action}\n• Time: {timestamp}',
        color: '#FF0000',
        footer: 'Auto-Moderation System'
      }
    },
    spamPrevention: {
      enabled: true,
      msgThreshold: 5,
      timeWindowMs: 10000,
      timeoutMs: 600000,
      action: 'timeout',
      whitelistedRoles: [], // Roles exempt from spam detection
      whitelistedChannels: [], // Channels where spam detection is disabled
      regexPatterns: [
        // { pattern: "buy\s+followers", flags: "i" }
      ], // Messages matching any pattern are treated as spam immediately
      deleteMessages: true,
      warnUser: true,
      dmUser: true,
      escalation: {
        enabled: true,
        secondOffenseAction: 'timeout',
        thirdOffenseAction: 'kick',
        severeCaseAction: 'ban',
        resetAfterMs: 3600000 // 1 hour
      },
      embedNotification: {
        enabled: true,
        title: '🚫 Spam Detected',
        description: 'User {user} was {action} for spamming.\n\n**Details:**\n• Messages: {messageCount} in {timeWindow}ms\n• Channel: {channel}\n• Action taken: {action}\n• Time: {timestamp}',
        color: '#FFA500',
        footer: 'Auto-Moderation System'
      }
    },
    autoMod: {
      enabled: true,
      regexFilters: [
        {
          name: 'profanity',
          description: 'Profanity filter with unicode support',
          pattern: "\\b(?:f(?:u|[u\\*\\@\\u200b\\u200c\\u200d\\u200a]){1,2}?c(?:k|c)|s(?:h[i!1\\*]*|h[!1])t|b(?:i[!1]tch|[i1]t)|d(?:i[!1]ck|[i1]ck)|c(?:u+n+t+)|p(?:u+s+y+|[u\\*\\@])|(?:a[!1]s[!1]h|[a@]ss(?:hole)?)|c(?:o+c+k+)|(?:w+h+o+r+e|g+[a@]+y|shlong))\\b",
          flags: 'gi',
          action: 'delete',
          punishment: 'warn'
        },
        {
          name: 'slurs',
          description: 'Zero tolerance for slurs',
          pattern: "\\b(n+[i!1]+g+[ge3]+[re3]+|f+[a@]+g+([o0]+t+)?|r+[e3]+t+[a@]+r+d+)\\b",
          flags: 'gi',
          action: 'delete',
          punishment: 'timeout',
          timeoutDuration: 3600000
        },
        {
          name: 'maliciousLinks',
          description: 'Block known malicious links',
          pattern: "(?:grabify\\.link|iplogger\\.(?:org|com|ru|co|info)|2no\\.co|yip\\.su|ipgrabber\\.ru|stealer\\.ru|ip-tracker\\.org|blasze\\.tk)",
          flags: 'gi',
          action: 'delete',
          punishment: 'timeout',
          timeoutDuration: 86400000
        },
        {
          name: 'phishingCommon',
          description: 'Common phishing attempts',
          pattern: "(?:(?:free[\\-.]?nitro)|(?:steam[\\-.]?community)|(?:discord[\\-.]?gift)|(?:nitro[\\-.]?free))",
          flags: 'gi',
          action: 'delete',
          punishment: 'timeout',
          timeoutDuration: 43200000
        },
        {
          name: 'phoneNumbers',
          description: 'Phone number protection',
          pattern: "\\b(?:\\+\\d{1,3}[-. ]?)?\\(?\\d{3}\\)?[-. ]?\\d{3}[-. ]?\\d{4}\\b",
          flags: 'g',
          action: 'delete',
          notifyMods: true
        },
        {
          name: 'emailAddresses',
          description: 'Email address protection',
          pattern: "([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}|[A-Za-z0-9+/=]+={0,2})",
          flags: 'gi',
          action: 'delete',
          notifyMods: true
        },
        {
          name: 'creditCards',
          description: 'Credit card number protection',
          pattern: "\\b(?:\\d[ -]*?){13,16}\\b",
          flags: 'g',
          action: 'delete',
          notifyMods: true
        },
        {
          name: 'invites',
          description: 'Discord invite filter',
          pattern: "(?:discord\\.(?:gg|com\\/invite)|discordapp\\.com\\/invite)\\/[a-zA-Z0-9-]+",
          flags: 'gi',
          action: 'filter',
          whitelist: ['your-server-invite']
        },
        {
          name: 'repetitiveText',
          description: 'Repetitive text spam',
          pattern: "(.{3,})\\1{4,}",
          flags: 'g',
          action: 'delete'
        },
        {
          name: 'massMention',
          description: 'Mass mention protection',
          pattern: "(<@!?\\d+>){3,}",
          flags: 'g',
          action: 'delete',
          punishment: 'timeout',
          timeoutDuration: 300000
        }
      ],
      profanityFilter: {
        enabled: true,
        severity: 'medium', // low, medium, high, strict
        action: 'warn', // warn, timeout, kick, ban
        customWords: [],
        whitelistedWords: [],
        deleteMessage: true
      },
      linkFilter: {
        enabled: false,
        allowedDomains: [],
        blockedDomains: [],
        action: 'warn',
        deleteMessage: true,
        whitelistedRoles: []
      },
      capsFilter: {
        enabled: false,
        threshold: 70, // Percentage of caps
        minLength: 10, // Minimum message length to check
        action: 'warn',
        deleteMessage: true
      },
      mentionSpam: {
        enabled: true,
        threshold: 5, // Max mentions per message
        action: 'timeout',
        deleteMessage: true,
        timeoutDuration: 600000 // 10 minutes
      },
      duplicateMessages: {
        enabled: true,
        threshold: 3, // Same message X times
        timeWindowMs: 30000, // Within X ms
        action: 'timeout',
        deleteMessage: true
      }
    },
    punishments: {
      warnings: {
        enabled: true,
        maxWarnings: 3,
        escalationAction: 'timeout', // timeout, kick, ban
        resetAfterMs: 2592000000, // 30 days
        dmUser: true,
        embedNotification: {
          enabled: true,
          title: '⚠️ Warning Issued',
          description: 'You have received a warning in {guild}.\n\n**Reason:** {reason}\n**Warning #{warningCount}** of {maxWarnings}\n**Moderator:** {moderator}',
          color: '#FFAA00',
          footer: 'Please follow the server rules'
        }
      },
      timeouts: {
        defaultDuration: 600000, // 10 minutes
        maxDuration: 2419200000, // 28 days
        dmUser: true,
        embedNotification: {
          enabled: true,
          title: '🔇 You have been timed out',
          description: 'You have been timed out in {guild}.\n\n**Reason:** {reason}\n**Duration:** {duration}\n**Moderator:** {moderator}\n**Expires:** {expiresAt}',
          color: '#3B82F6',
          footer: 'Timeout will be automatically lifted'
        }
      },
      kicks: {
        dmUser: true,
        embedNotification: {
          enabled: true,
          title: '👢 You have been kicked',
          description: 'You have been kicked from {guild}.\n\n**Reason:** {reason}\n**Moderator:** {moderator}\n**Time:** {timestamp}',
          color: '#F59E0B',
          footer: 'You can rejoin the server'
        }
      },
      bans: {
        dmUser: true,
        deleteMessageDays: 1,
        embedNotification: {
          enabled: true,
          title: '🔨 You have been banned',
          description: 'You have been banned from {guild}.\n\n**Reason:** {reason}\n**Moderator:** {moderator}\n**Time:** {timestamp}',
          color: '#EF4444',
          footer: 'This action is permanent unless appealed'
        }
      }
    },
    logging: {
      enabled: true,
      channelId: null,
      logRaidProtection: true,
      logSpamPrevention: true,
      logAutoMod: true,
      logManualActions: true,
      logWarnings: true,
      logTimeouts: true,
      logKicks: true,
      logBans: true,
      embedLogs: true,
      detailedLogs: true
    },
    appeals: {
      enabled: false,
      channelId: null,
      allowedFor: ['ban', 'kick'], // What punishments can be appealed
      cooldownMs: 86400000, // 24 hours between appeals
      requireReason: true,
      autoCreateThread: true
    },
    bypass: {
      enabled: true,
      rules: [
        {
          id: 'admin-bypass',
          name: 'Administrator Bypass',
          description: 'Administrators bypass all moderation actions',
          roleIds: [], // Role IDs that get this bypass
          bypassFeatures: ['raidProtection', 'spamPrevention', 'autoMod', 'punishments'],
          priority: 100, // Higher priority = applied first
          enabled: true
        },
        {
          id: 'moderator-bypass',
          name: 'Moderator Bypass',
          description: 'Moderators bypass automatic moderation but not manual actions',
          roleIds: [], // Role IDs that get this bypass
          bypassFeatures: ['raidProtection', 'spamPrevention', 'autoMod'],
          priority: 50,
          enabled: true
        }
      ],
      inheritanceMode: 'highest', // 'highest' | 'additive' - How to handle multiple role bypasses
      logBypassEvents: true // Log when bypass rules are applied
    }
  }
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Get guild ID from dashboard config instead of session
  const guildId = dashboardConfig.bot.guildId;
  if (!guildId) {
    return res.status(400).json({ error: 'Guild ID not found in configuration.' });
  }

  try {
    const db = await getDb();

    if (req.method === 'GET') {
      const guildConfig = await db.collection('guild_configs').findOne({ guildId });

      const settings = {
        moderation: guildConfig?.moderation || defaultSettings.moderation
      };

      return res.status(200).json(settings);
    }

    if (req.method === 'POST') {
      const { moderation } = req.body;

      // Basic validation
      if (!moderation || typeof moderation !== 'object') {
        return res.status(400).json({ error: 'Invalid moderation data format.' });
      }

      // Validate raid protection settings
      if (moderation.raidProtection) {
        if (typeof moderation.raidProtection.enabled !== 'boolean') {
          return res.status(400).json({ error: 'Invalid raid protection enabled flag.' });
        }
        if (moderation.raidProtection.enabled) {
          if (typeof moderation.raidProtection.joinThreshold !== 'number' || 
              moderation.raidProtection.joinThreshold < 2 || 
              moderation.raidProtection.joinThreshold > 50) {
            return res.status(400).json({ error: 'Join threshold must be between 2 and 50.' });
          }
          if (typeof moderation.raidProtection.timeWindowMs !== 'number' || 
              moderation.raidProtection.timeWindowMs < 1000 || 
              moderation.raidProtection.timeWindowMs > 300000) {
            return res.status(400).json({ error: 'Time window must be between 1 and 300 seconds.' });
          }
          if (!['ban', 'kick'].includes(moderation.raidProtection.mitigationAction)) {
            return res.status(400).json({ error: 'Invalid mitigation action.' });
          }
        }
      }

      // Validate spam prevention settings
      if (moderation.spamPrevention) {
        if (typeof moderation.spamPrevention.enabled !== 'boolean') {
          return res.status(400).json({ error: 'Invalid spam prevention enabled flag.' });
        }
        if (moderation.spamPrevention.enabled) {
          if (typeof moderation.spamPrevention.msgThreshold !== 'number' || 
              moderation.spamPrevention.msgThreshold < 2 || 
              moderation.spamPrevention.msgThreshold > 50) {
            return res.status(400).json({ error: 'Message threshold must be between 2 and 50.' });
          }
          if (typeof moderation.spamPrevention.timeWindowMs !== 'number' || 
              moderation.spamPrevention.timeWindowMs < 1000 || 
              moderation.spamPrevention.timeWindowMs > 300000) {
            return res.status(400).json({ error: 'Time window must be between 1 and 300 seconds.' });
          }
          if (!['timeout', 'kick', 'ban'].includes(moderation.spamPrevention.action)) {
            return res.status(400).json({ error: 'Invalid spam prevention action.' });
          }
        }
      }

      // Validate logging settings
      if (moderation.logging) {
        if (typeof moderation.logging.enabled !== 'boolean') {
          return res.status(400).json({ error: 'Invalid logging enabled flag.' });
        }
        if (moderation.logging.enabled && moderation.logging.channelId && 
            typeof moderation.logging.channelId !== 'string') {
          return res.status(400).json({ error: 'Invalid log channel ID.' });
        }
      }

      const updateData = {
        guildId,
        moderation: {
          raidProtection: {
            enabled: moderation.raidProtection?.enabled || false,
            joinThreshold: moderation.raidProtection?.joinThreshold || 5,
            timeWindowMs: moderation.raidProtection?.timeWindowMs || 10000,
            mitigationDurationMs: moderation.raidProtection?.mitigationDurationMs || 300000,
            mitigationAction: moderation.raidProtection?.mitigationAction || 'ban'
          },
          spamPrevention: {
            enabled: moderation.spamPrevention?.enabled || false,
            msgThreshold: moderation.spamPrevention?.msgThreshold || 5,
            timeWindowMs: moderation.spamPrevention?.timeWindowMs || 10000,
            timeoutMs: moderation.spamPrevention?.timeoutMs || 600000,
            action: moderation.spamPrevention?.action || 'timeout'
          },
          autoMod: {
            enabled: moderation.autoMod?.enabled || false,
            deleteOffensiveMessages: moderation.autoMod?.deleteOffensiveMessages || true,
            warnOnFirstOffense: moderation.autoMod?.warnOnFirstOffense || true,
            timeoutOnRepeat: moderation.autoMod?.timeoutOnRepeat || true,
            banOnSevere: moderation.autoMod?.banOnSevere || true
          },
          logging: {
            enabled: moderation.logging?.enabled || false,
            channelId: moderation.logging?.channelId || null,
            logRaidProtection: moderation.logging?.logRaidProtection !== false,
            logSpamPrevention: moderation.logging?.logSpamPrevention !== false,
            logAutoMod: moderation.logging?.logAutoMod !== false,
            logManualActions: moderation.logging?.logManualActions !== false
          }
        }
      };

      await db.collection('guild_configs').updateOne(
        { guildId },
        { $set: updateData },
        { upsert: true }
      );

      return res.status(200).json({ message: 'Moderation settings saved successfully.' });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Moderation API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 