// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { 
  FiMessageSquare, 
  FiLogOut, 
  FiPlus, 
  FiTrash2, 
  FiEdit2, 
  FiEye, 
  FiCopy, 
  FiShuffle, 
  FiSettings, 
  FiHash 
} from 'react-icons/fi';
import { useTheme } from '../contexts/ThemeContext';

// Message template interface
interface MessageTemplate {
  title?: string;
  description: string;
  color?: string;
  footer?: string;
}

export default function WelcomeSystemDialog({ 
  isOpen, 
  onClose, 
  channels = [], 
  roles = [] 
}) {
  const { currentScheme } = useTheme();
  const [settings, setSettings] = useState({
    welcome: {
      enabled: false,
      channelId: '',
      messages: []
    },
    goodbye: {
      enabled: false,
      channelId: '',
      messages: []
    }
  });
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'welcome' | 'goodbye' | 'placeholders'>('welcome');

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
      style={{ backgroundColor: `${currentScheme.colors.background}80` }}
    >
      <div 
        className="relative w-full max-w-6xl mx-auto my-8 rounded-lg shadow-2xl"
        style={{ 
          backgroundColor: currentScheme.colors.surface,
          border: `2px solid ${currentScheme.colors.border}` 
        }}
      >
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FiMessageSquare className="w-6 h-6 text-blue-500" />
            <h2 
              className="text-xl font-bold"
              style={{ color: currentScheme.colors.text }}
            >
              Welcome & Goodbye System
            </h2>
          </div>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            ✕
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-4 px-6 pt-4">
            <button 
              onClick={() => setActiveTab('welcome')}
              className={`
                py-2 px-4 text-sm font-medium 
                ${activeTab === 'welcome' 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'}
              `}
            >
              <div className="flex items-center space-x-2">
                <FiMessageSquare />
                <span>Welcome Messages</span>
              </div>
            </button>
            <button 
              onClick={() => setActiveTab('goodbye')}
              className={`
                py-2 px-4 text-sm font-medium 
                ${activeTab === 'goodbye' 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'}
              `}
            >
              <div className="flex items-center space-x-2">
                <FiLogOut />
                <span>Goodbye Messages</span>
              </div>
            </button>
            <button 
              onClick={() => setActiveTab('placeholders')}
              className={`
                py-2 px-4 text-sm font-medium 
                ${activeTab === 'placeholders' 
                  ? 'border-b-2 border-blue-500 text-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'}
              `}
            >
              <div className="flex items-center space-x-2">
                <FiSettings />
                <span>Placeholders</span>
              </div>
            </button>
          </nav>
        </div>

        {/* Content Area */}
        <div className="p-6 max-h-[70vh] overflow-y-auto">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-blue-500"></div>
            </div>
          ) : (
            <div>
              {activeTab === 'welcome' && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <FiMessageSquare className="w-5 h-5 text-blue-500" />
                      <span className="font-semibold">Welcome Messages</span>
                    </div>
                    <label className="flex items-center space-x-2">
                      <input 
                        type="checkbox" 
                        checked={settings.welcome.enabled}
                        onChange={() => {}}
                        className="form-checkbox text-blue-500"
                      />
                      <span>Enabled</span>
                    </label>
                  </div>
                  
                  {/* Channel Selection */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Welcome Channel
                    </label>
                    <select 
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200"
                    >
                      {channels.map((channel) => (
                        <option key={channel.id} value={channel.id}>
                          {channel.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 space-x-3">
          <button 
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 rounded-md border border-gray-300"
          >
            Cancel
          </button>
          <button 
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
}
  