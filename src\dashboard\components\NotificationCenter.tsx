import React, { useState } from 'react';
import { FiBell } from 'react-icons/fi';
import { useSession } from 'next-auth/react';

// Lightweight notification center - minimal functionality to reduce bundle size
export default function NotificationCenter() {
  const { data: session } = useSession();
  const [notifications] = useState<any[]>([]); // Empty for now to reduce complexity
  
  // Don't render if no session
  if (!session?.user) {
    return null;
  }

  const unreadCount = 0; // Simplified for now

  return (
    <div className="relative">
      <div className="relative">
        <button 
          aria-label="Notifications"
          className="text-gray-300 hover:text-white hover:scale-105 transition-all group"
        >
          <FiBell className="w-5 h-5 group-hover:rotate-12" />
        </button>
        
        {unreadCount > 0 && (
          <span 
            className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full 
            w-4 h-4 flex items-center justify-center text-xs font-bold"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </div>

      {/* Popover content would be added here in a future implementation */}
    </div>
  );
} 