import React, { useState, useEffect } from 'react';
import { 
  FiAlertTriangle, 
  FiShield, 
  FiTrash2, 
  FiLock, 
  FiSettings, 
  FiMessageSquare, 
  FiUserX, 
  FiFlag,
  FiLink,
  FiVolume2,
  FiCopy,
  FiFilter,
  FiUsers,
  FiClock
} from 'react-icons/fi';
import { useTheme } from '../contexts/ThemeContext';

interface ModerationSystemDialogProps {
  isOpen: boolean;
  onClose: () => void;
  channels: any[]; // Replace with actual channel type
  roles: any[]; // Replace with actual role type
}

const ModerationSystemDialog: React.FC<ModerationSystemDialogProps> = ({ 
  isOpen, 
  onClose, 
  channels, 
  roles 
}) => {
  const { currentScheme } = useTheme();
  const [settings, setSettings] = useState({
    moderation: {
      raidProtection: {
        enabled: true,
        joinThreshold: 5,
        timeWindowMs: 10000,
        mitigationDurationMs: 300000,
        mitigationAction: 'ban',
        whitelistedRoles: [],
        notifyChannel: null,
        dmUser: true,
        embedNotification: {
          enabled: true,
          title: '🛡️ Raid Protection Triggered',
          description: 'User {user} was {action} due to raid protection.\n\n**Details:**\n• Join threshold exceeded: {threshold} users in {timeWindow}ms\n• Action taken: {action}\n• Time: {timestamp}',
          color: '#FF0000',
          footer: 'Auto-Moderation System'
        }
      },
      spamPrevention: {
        enabled: true,
        msgThreshold: 5,
        timeWindowMs: 10000,
        timeoutMs: 600000,
        action: 'timeout',
        whitelistedRoles: [],
        whitelistedChannels: [],
        deleteMessages: true,
        warnUser: true,
        dmUser: true,
        escalation: {
          enabled: true,
          secondOffenseAction: 'timeout',
          thirdOffenseAction: 'kick',
          severeCaseAction: 'ban',
          resetAfterMs: 3600000
        },
        embedNotification: {
          enabled: true,
          title: '🚫 Spam Detected',
          description: 'User {user} was {action} for spamming.\n\n**Details:**\n• Messages: {messageCount} in {timeWindow}ms\n• Channel: {channel}\n• Action taken: {action}\n• Time: {timestamp}',
          color: '#FFA500',
          footer: 'Auto-Moderation System'
        }
      },
      autoMod: {
        enabled: true,
        regexFilters: [],
        profanityFilter: {
          enabled: true,
          severity: 'medium',
          action: 'warn',
          customWords: [],
          whitelistedWords: [],
          deleteMessage: true
        },
        linkFilter: {
          enabled: false,
          allowedDomains: [],
          blockedDomains: [],
          action: 'warn',
          deleteMessage: true,
          whitelistedRoles: []
        },
        capsFilter: {
          enabled: false,
          threshold: 70,
          minLength: 10,
          action: 'warn',
          deleteMessage: true
        },
        mentionSpam: {
          enabled: true,
          threshold: 5,
          action: 'timeout',
          deleteMessage: true,
          timeoutDuration: 600000
        },
        duplicateMessages: {
          enabled: true,
          threshold: 3,
          timeWindowMs: 30000,
          action: 'timeout',
          deleteMessage: true
        }
      },
      punishments: {
        warnings: {
          enabled: true,
          maxWarnings: 3,
          escalationAction: 'timeout',
          resetAfterMs: 2592000000,
          dmUser: true,
          embedNotification: {
            enabled: true,
            title: '⚠️ Warning Issued',
            description: 'You have received a warning in {guild}.\n\n**Reason:** {reason}\n**Warning #{warningCount}** of {maxWarnings}\n**Moderator:** {moderator}',
            color: '#FFAA00',
            footer: 'Please follow the server rules'
          }
        },
        timeouts: {
          defaultDuration: 600000,
          maxDuration: 2419200000,
          dmUser: true,
          embedNotification: {
            enabled: true,
            title: '🔇 You have been timed out',
            description: 'You have been timed out in {guild}.\n\n**Reason:** {reason}\n**Duration:** {duration}\n**Moderator:** {moderator}\n**Expires:** {expiresAt}',
            color: '#3B82F6',
            footer: 'Timeout will be automatically lifted'
          }
        },
        kicks: {
          dmUser: true,
          embedNotification: {
            enabled: true,
            title: '👢 You have been kicked',
            description: 'You have been kicked from {guild}.\n\n**Reason:** {reason}\n**Moderator:** {moderator}\n**Time:** {timestamp}',
            color: '#F59E0B',
            footer: 'You can rejoin the server'
          }
        },
        bans: {
          dmUser: true,
          deleteMessageDays: 1,
          embedNotification: {
            enabled: true,
            title: '🔨 You have been banned',
            description: 'You have been banned from {guild}.\n\n**Reason:** {reason}\n**Moderator:** {moderator}\n**Time:** {timestamp}',
            color: '#EF4444',
            footer: 'This action is permanent unless appealed'
          }
        }
      },
      logging: {
        enabled: true,
        channelId: '',
        logRaidProtection: true,
        logSpamPrevention: true,
        logAutoMod: true,
        logManualActions: true,
        logWarnings: true,
        logTimeouts: true,
        logKicks: true,
        logBans: true,
        embedLogs: true,
        detailedLogs: true
      },
      appeals: {
        enabled: false,
        channelId: null,
        allowedFor: ['ban', 'kick'],
        cooldownMs: 86400000,
        requireReason: true,
        autoCreateThread: true
      },
      bypass: {
        enabled: true,
        rules: [
          {
            id: 'admin-bypass',
            name: 'Administrator Bypass',
            description: 'Administrators bypass all moderation actions',
            roleIds: [],
            bypassFeatures: ['raidProtection', 'spamPrevention', 'autoMod', 'punishments'],
            priority: 100,
            enabled: true
          },
          {
            id: 'moderator-bypass',
            name: 'Moderator Bypass',
            description: 'Moderators bypass automatic moderation but not manual actions',
            roleIds: [],
            bypassFeatures: ['raidProtection', 'spamPrevention', 'autoMod'],
            priority: 50,
            enabled: true
          }
        ],
        inheritanceMode: 'highest',
        logBypassEvents: true
      }
    }
  });
  const [loading, setLoading] = useState(false);

  // Include text channels and announcement channels as they can both receive messages
  // Channel types can be either numbers or strings depending on the API response
  const textChannels = channels?.filter(c =>
    c.type === 0 || c.type === 5 ||
    c.type === 'GUILD_TEXT' || c.type === 'GUILD_ANNOUNCEMENT'
  ) || [];

  // Load current configuration when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadCurrentConfig();
    }
  }, [isOpen]);

  // Debug logging for channels
  useEffect(() => {
    console.log('ModerationSystemDialog - Channels:', channels);
    console.log('ModerationSystemDialog - Text channels:', textChannels);
  }, [channels, textChannels]);

  const loadCurrentConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/automation/moderation', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Failed to load moderation config:', error);
      toast({
        title: 'Error loading settings',
        description: 'Failed to load current moderation settings',
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const response = await fetch('/api/automation/moderation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      });

      if (!response.ok) {
        throw new Error('Failed to save moderation configuration');
      }

      toast({
        title: 'Moderation Settings Updated',
        description: 'Your moderation system configuration has been saved.',
        status: 'success',
        duration: 3000,
        isClosable: true
      });

      onClose();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  };

  if (loading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="6xl">
        <ModalOverlay backdropFilter="blur(10px)" />
        <ModalContent>
          <ModalBody>
            <VStack justify="center" h="400px">
              <Spinner size="xl" />
              <Text>Loading moderation settings...</Text>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      size="6xl" 
      scrollBehavior="inside"
    >
      <ModalOverlay 
        bg="blackAlpha.700" 
        backdropFilter="blur(15px)" 
      />
      <ModalContent 
        maxH="90vh" 
        bg={currentScheme.colors.background} 
        border="3px solid" 
        borderColor="red.500" 
        boxShadow="0 0 30px rgba(255,0,0,0.2)"
      >
        <ModalHeader 
          bg="red.50" 
          borderBottom="1px solid" 
          borderColor="red.200" 
          _dark={{ 
            bg: "red.900", 
            borderColor: "red.700" 
          }}
        >
          <HStack>
            <Icon 
              as={FiShield} 
              boxSize={7} 
              color="red.500" 
              mr={3} 
              animation="pulse 2s infinite"
              sx={{
                '@keyframes pulse': {
                  '0%, 100%': { transform: 'scale(1)' },
                  '50%': { transform: 'scale(1.1)' }
                }
              }}
            />
            <Text 
              fontWeight="bold" 
              color="red.600" 
              _dark={{ 
                color: "red.300" 
              }}
            >
              Advanced Auto-Moderation System
            </Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <Tabs 
            isFitted 
            variant="enclosed" 
            colorScheme="red"
            bg="red.50" 
            _dark={{ bg: "red.900" }}
          >
            <TabList 
              borderBottom="2px solid" 
              borderColor="red.200" 
              _dark={{ 
                borderColor: "red.700" 
              }}
            >
              <Tab 
                _selected={{ 
                  bg: 'red.100', 
                  color: 'red.700', 
                  borderColor: 'red.300',
                  _dark: {
                    bg: 'red.800',
                    color: 'red.200',
                    borderColor: 'red.600'
                  }
                }}
              >
                <Icon as={FiShield} mr={2} /> Protection
              </Tab>
              <Tab 
                _selected={{ 
                  bg: 'red.100', 
                  color: 'red.700', 
                  borderColor: 'red.300',
                  _dark: {
                    bg: 'red.800',
                    color: 'red.200',
                    borderColor: 'red.600'
                  }
                }}
              >
                <Icon as={FiFilter} mr={2} /> Auto-Mod
              </Tab>
              <Tab 
                _selected={{ 
                  bg: 'red.100', 
                  color: 'red.700', 
                  borderColor: 'red.300',
                  _dark: {
                    bg: 'red.800',
                    color: 'red.200',
                    borderColor: 'red.600'
                  }
                }}
              >
                <Icon as={FiUserX} mr={2} /> Punishments
              </Tab>
              <Tab 
                _selected={{ 
                  bg: 'red.100', 
                  color: 'red.700', 
                  borderColor: 'red.300',
                  _dark: {
                    bg: 'red.800',
                    color: 'red.200',
                    borderColor: 'red.600'
                  }
                }}
              >
                <Icon as={FiMessageSquare} mr={2} /> Logging
              </Tab>
              <Tab 
                _selected={{ 
                  bg: 'red.100', 
                  color: 'red.700', 
                  borderColor: 'red.300',
                  _dark: {
                    bg: 'red.800',
                    color: 'red.200',
                    borderColor: 'red.600'
                  }
                }}
              >
                <Icon as={FiFlag} mr={2} /> Appeals
              </Tab>
              <Tab 
                _selected={{ 
                  bg: 'red.100', 
                  color: 'red.700', 
                  borderColor: 'red.300',
                  _dark: {
                    bg: 'red.800',
                    color: 'red.200',
                    borderColor: 'red.600'
                  }
                }}
              >
                <Icon as={FiUsers} mr={2} /> Bypass
              </Tab>
              <Tab 
                _selected={{ 
                  bg: 'red.100', 
                  color: 'red.700', 
                  borderColor: 'red.300',
                  _dark: {
                    bg: 'red.800',
                    color: 'red.200',
                    borderColor: 'red.600'
                  }
                }}
              >
                <Icon as={FiCopy} mr={2} /> Placeholders
              </Tab>
            </TabList>

            <TabPanels>
              {/* Protection Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {/* Raid Protection */}
                  <Card>
                    <CardHeader>
                      <HStack justify="space-between">
                        <Heading size="md">
                          <Icon as={FiAlertTriangle} mr={2} color="red.400" />
                          Raid Protection
                        </Heading>
                        {/* Add tooltips and visual indicators for critical settings */}
                        <Tooltip 
                          label="Raid Protection prevents mass join attacks by automatically taking action against suspicious user activity" 
                          placement="top"
                          hasArrow
                          bg="red.600"
                          color="white"
                        >
                          <HStack 
                            spacing={4} 
                            align="center" 
                            p={2} 
                            bg={settings.moderation.raidProtection.enabled ? 'red.50' : 'gray.50'} 
                            borderRadius="md"
                            _dark={{
                              bg: settings.moderation.raidProtection.enabled ? 'red.900' : 'gray.800'
                            }}
                          >
                            <Icon 
                              as={FiAlertTriangle} 
                              boxSize={6} 
                              color={settings.moderation.raidProtection.enabled ? 'red.500' : 'gray.400'}
                              animation={settings.moderation.raidProtection.enabled ? "pulse 2s infinite" : "none"}
                              sx={{
                                '@keyframes pulse': {
                                  '0%, 100%': { transform: 'scale(1)' },
                                  '50%': { transform: 'scale(1.1)' }
                                }
                              }}
                            />
                            <VStack align="start" spacing={0}>
                              <Text 
                                fontWeight="bold" 
                                color={settings.moderation.raidProtection.enabled ? 'red.600' : 'gray.600'}
                                _dark={{
                                  color: settings.moderation.raidProtection.enabled ? 'red.300' : 'gray.400'
                                }}
                              >
                                Raid Protection
                              </Text>
                              <Text 
                                fontSize="xs" 
                                color={settings.moderation.raidProtection.enabled ? 'red.500' : 'gray.500'}
                                _dark={{
                                  color: settings.moderation.raidProtection.enabled ? 'red.400' : 'gray.500'
                                }}
                              >
                                {settings.moderation.raidProtection.enabled 
                                  ? `Active: Protecting against ${settings.moderation.raidProtection.joinThreshold} joins in ${settings.moderation.raidProtection.timeWindowMs}ms` 
                                  : 'Inactive: No protection against mass joins'}
                              </Text>
                            </VStack>
                            <Flex flex={1} />
                            <Switch 
                              colorScheme="red"
                              size="lg"
                              isChecked={settings.moderation.raidProtection.enabled}
                              onChange={(e) => setSettings(prev => ({
                                ...prev, 
                                moderation: {
                                  ...prev.moderation,
                                  raidProtection: { 
                                    ...prev.moderation.raidProtection, 
                                    enabled: e.target.checked 
                                  }
                                }
                              }))}
                              sx={{
                                'span.chakra-switch__track': {
                                  bg: settings.moderation.raidProtection.enabled ? 'red.600' : 'gray.300',
                                  _dark: {
                                    bg: settings.moderation.raidProtection.enabled ? 'red.800' : 'gray.600'
                                  }
                                },
                                'span.chakra-switch__thumb': {
                                  bg: settings.moderation.raidProtection.enabled ? 'red.100' : 'gray.100'
                                }
                              }}
                            />
                          </HStack>
                        </Tooltip>
                        {settings.moderation.raidProtection.enabled && (
                          <Alert 
                            status="warning" 
                            variant="left-accent" 
                            mb={4} 
                            borderRadius="md"
                            colorScheme="red"
                          >
                            <AlertIcon />
                            <Box>
                              <AlertTitle>Raid Protection Activated</AlertTitle>
                              <AlertDescription>
                                Your server is currently protected against potential raid attacks. 
                                Current settings will {settings.moderation.raidProtection.mitigationAction} users exceeding {settings.moderation.raidProtection.joinThreshold} joins in {settings.moderation.raidProtection.timeWindowMs}ms.
                              </AlertDescription>
                            </Box>
                          </Alert>
                        )}
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        <FormControl>
                          <FormLabel>Join Threshold</FormLabel>
                          <NumberInput 
                            value={settings.moderation.raidProtection.joinThreshold}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                raidProtection: { 
                                  ...prev.moderation.raidProtection, 
                                  joinThreshold: val || 5
                                }
                              }
                            }))}
                            min={2} max={50}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                          <Text fontSize="sm" color="gray.500">Users joining within time window</Text>
                        </FormControl>

                        <FormControl>
                          <FormLabel>Time Window (seconds)</FormLabel>
                          <NumberInput 
                            value={settings.moderation.raidProtection.timeWindowMs / 1000}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                raidProtection: { 
                                  ...prev.moderation.raidProtection, 
                                  timeWindowMs: (val || 10) * 1000
                                }
                              }
                            }))}
                            min={1} max={300}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                        </FormControl>

                        <FormControl>
                          <FormLabel>Mitigation Action</FormLabel>
                          <Select 
                            value={settings.moderation.raidProtection.mitigationAction}
                            onChange={(e) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                raidProtection: { 
                                  ...prev.moderation.raidProtection, 
                                  mitigationAction: e.target.value 
                                }
                              }
                            }))}
                          >
                            <option value="ban">Ban</option>
                            <option value="kick">Kick</option>
                          </Select>
                        </FormControl>

                        <FormControl>
                          <FormLabel>Mitigation Duration (minutes)</FormLabel>
                          <NumberInput 
                            value={settings.moderation.raidProtection.mitigationDurationMs / 60000}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                raidProtection: { 
                                  ...prev.moderation.raidProtection, 
                                  mitigationDurationMs: (val || 5) * 60000
                                }
                              }
                            }))}
                            min={1} max={1440}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                          <Text fontSize="sm" color="gray.500">How long to keep protection active</Text>
                        </FormControl>
                      </SimpleGrid>

                      <VStack spacing={4} align="stretch" mt={4}>
                        <FormControl display="flex" alignItems="center">
                          <FormLabel flex="1">Send DM to affected users</FormLabel>
                          <Switch 
                            colorScheme="red"
                            isChecked={settings.moderation.raidProtection.dmUser}
                            onChange={(e) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                raidProtection: { 
                                  ...prev.moderation.raidProtection, 
                                  dmUser: e.target.checked 
                                }
                              }
                            }))}
                          />
                        </FormControl>

                        <Accordion allowToggle>
                          <AccordionItem>
                            <AccordionButton>
                              <Box flex="1" textAlign="left">
                                <Icon as={FiMessageSquare} mr={2} />
                                Embed Notification Settings
                              </Box>
                              <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel>
                              <VStack spacing={4} align="stretch">
                                <FormControl display="flex" alignItems="center">
                                  <FormLabel flex="1">Enable Embed Notifications</FormLabel>
                                  <Switch 
                                    colorScheme="red"
                                    isChecked={settings.moderation.raidProtection.embedNotification.enabled}
                                    onChange={(e) => setSettings(prev => ({
                                      ...prev, 
                                      moderation: {
                                        ...prev.moderation,
                                        raidProtection: { 
                                          ...prev.moderation.raidProtection, 
                                          embedNotification: {
                                            ...prev.moderation.raidProtection.embedNotification,
                                            enabled: e.target.checked
                                          }
                                        }
                                      }
                                    }))}
                                  />
                                </FormControl>
                                
                                <FormControl>
                                  <FormLabel>Embed Title</FormLabel>
                                  <Input 
                                    value={settings.moderation.raidProtection.embedNotification.title}
                                    onChange={(e) => setSettings(prev => ({
                                      ...prev, 
                                      moderation: {
                                        ...prev.moderation,
                                        raidProtection: { 
                                          ...prev.moderation.raidProtection, 
                                          embedNotification: {
                                            ...prev.moderation.raidProtection.embedNotification,
                                            title: e.target.value
                                          }
                                        }
                                      }
                                    }))}
                                  />
                                </FormControl>

                                <FormControl>
                                  <FormLabel>Embed Description</FormLabel>
                                  <Textarea 
                                    value={settings.moderation.raidProtection.embedNotification.description}
                                    onChange={(e) => setSettings(prev => ({
                                      ...prev, 
                                      moderation: {
                                        ...prev.moderation,
                                        raidProtection: { 
                                          ...prev.moderation.raidProtection, 
                                          embedNotification: {
                                            ...prev.moderation.raidProtection.embedNotification,
                                            description: e.target.value
                                          }
                                        }
                                      }
                                    }))}
                                    rows={4}
                                  />
                                                          <Text fontSize="sm" color="gray.500" mt={1}>
                          Available placeholders: {'{user}'}, {'{action}'}, {'{threshold}'}, {'{timeWindow}'}, {'{timestamp}'}, {'{guild}'}, {'{moderator}'}, {'{reason}'}
                        </Text>
                                </FormControl>

                                <FormControl>
                                  <FormLabel>Embed Color</FormLabel>
                                  <Input 
                                    type="color"
                                    value={settings.moderation.raidProtection.embedNotification.color}
                                    onChange={(e) => setSettings(prev => ({
                                      ...prev, 
                                      moderation: {
                                        ...prev.moderation,
                                        raidProtection: { 
                                          ...prev.moderation.raidProtection, 
                                          embedNotification: {
                                            ...prev.moderation.raidProtection.embedNotification,
                                            color: e.target.value
                                          }
                                        }
                                      }
                                    }))}
                                    w="100px"
                                  />
                                </FormControl>
                              </VStack>
                            </AccordionPanel>
                          </AccordionItem>
                        </Accordion>
                      </VStack>
                    </CardBody>
                  </Card>

                  {/* Spam Prevention */}
                  <Card>
                    <CardHeader>
                      <HStack justify="space-between">
                        <Heading size="md">
                          <Icon as={FiTrash2} mr={2} color="orange.400" />
                          Spam Prevention
                        </Heading>
                        <Switch 
                          colorScheme="red"
                          size="lg"
                          isChecked={settings.moderation.spamPrevention.enabled}
                          onChange={(e) => setSettings(prev => ({
                            ...prev, 
                            moderation: {
                              ...prev.moderation,
                              spamPrevention: { 
                                ...prev.moderation.spamPrevention, 
                                enabled: e.target.checked 
                              }
                            }
                          }))}
                        />
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        <FormControl>
                          <FormLabel>Message Threshold</FormLabel>
                          <NumberInput 
                            value={settings.moderation.spamPrevention.msgThreshold}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                spamPrevention: { 
                                  ...prev.moderation.spamPrevention, 
                                  msgThreshold: val || 5
                                }
                              }
                            }))}
                            min={2} max={50}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                        </FormControl>

                        <FormControl>
                          <FormLabel>Time Window (seconds)</FormLabel>
                          <NumberInput 
                            value={settings.moderation.spamPrevention.timeWindowMs / 1000}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                spamPrevention: { 
                                  ...prev.moderation.spamPrevention, 
                                  timeWindowMs: (val || 10) * 1000
                                }
                              }
                            }))}
                            min={1} max={300}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                        </FormControl>

                        <FormControl>
                          <FormLabel>Primary Action</FormLabel>
                          <Select 
                            value={settings.moderation.spamPrevention.action}
                            onChange={(e) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                spamPrevention: { 
                                  ...prev.moderation.spamPrevention, 
                                  action: e.target.value 
                                }
                              }
                            }))}
                          >
                            <option value="warn">Warn</option>
                            <option value="timeout">Timeout</option>
                            <option value="kick">Kick</option>
                            <option value="ban">Ban</option>
                          </Select>
                        </FormControl>

                        <FormControl>
                          <FormLabel>Timeout Duration (minutes)</FormLabel>
                          <NumberInput 
                            value={settings.moderation.spamPrevention.timeoutMs / 60000}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                spamPrevention: { 
                                  ...prev.moderation.spamPrevention, 
                                  timeoutMs: (val || 10) * 60000
                                }
                              }
                            }))}
                            min={1} max={40320}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                        </FormControl>
                      </SimpleGrid>

                      <VStack spacing={4} align="stretch" mt={4}>
                        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                          <FormControl display="flex" alignItems="center">
                            <FormLabel flex="1" mr={2}>Delete Messages</FormLabel>
                            <Switch 
                              colorScheme="red"
                              isChecked={settings.moderation.spamPrevention.deleteMessages}
                              onChange={(e) => setSettings(prev => ({
                                ...prev, 
                                moderation: {
                                  ...prev.moderation,
                                  spamPrevention: { 
                                    ...prev.moderation.spamPrevention, 
                                    deleteMessages: e.target.checked 
                                  }
                                }
                              }))}
                              sx={{
                                'span.chakra-switch__track': {
                                  bg: settings.moderation.spamPrevention.deleteMessages ? 'red.600' : 'gray.300',
                                  _dark: {
                                    bg: settings.moderation.spamPrevention.deleteMessages ? 'red.800' : 'gray.600'
                                  }
                                },
                                'span.chakra-switch__thumb': {
                                  bg: settings.moderation.spamPrevention.deleteMessages ? 'red.100' : 'gray.100'
                                }
                              }}
                            />
                          </FormControl>

                          <FormControl display="flex" alignItems="center">
                            <FormLabel flex="1" mr={2}>Warn User</FormLabel>
                            <Switch 
                              colorScheme="red"
                              isChecked={settings.moderation.spamPrevention.warnUser}
                              onChange={(e) => setSettings(prev => ({
                                ...prev, 
                                moderation: {
                                  ...prev.moderation,
                                  spamPrevention: { 
                                    ...prev.moderation.spamPrevention, 
                                    warnUser: e.target.checked 
                                  }
                                }
                              }))}
                            />
                          </FormControl>

                          <FormControl display="flex" alignItems="center">
                            <FormLabel flex="1" mr={2}>Send DM</FormLabel>
                            <Switch 
                              colorScheme="red"
                              isChecked={settings.moderation.spamPrevention.dmUser}
                              onChange={(e) => setSettings(prev => ({
                                ...prev, 
                                moderation: {
                                  ...prev.moderation,
                                  spamPrevention: { 
                                    ...prev.moderation.spamPrevention, 
                                    dmUser: e.target.checked 
                                  }
                                }
                              }))}
                            />
                          </FormControl>
                        </SimpleGrid>
                      </VStack>
                    </CardBody>
                  </Card>
                </VStack>
              </TabPanel>

              {/* Auto-Mod Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Card>
                    <CardHeader>
                      <HStack justify="space-between">
                        <Heading size="md">
                          <Icon as={FiFilter} mr={2} color="blue.400" />
                          Regex Filters
                        </Heading>
                        <Switch
                          colorScheme="red"
                          size="lg"
                          isChecked={settings.moderation.autoMod.enabled}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            moderation: {
                              ...prev.moderation,
                              autoMod: {
                                ...prev.moderation.autoMod,
                                enabled: e.target.checked
                              }
                            }
                          }))}
                        />
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={4} align="stretch">
                        <HStack justify="space-between">
                          <Heading size="sm">Filters</Heading>
                          <Button
                            size="sm"
                            colorScheme="blue"
                            onClick={() => {
                              const newFilter = {
                                id: `regex-${Date.now()}`,
                                name: 'New Filter',
                                pattern: '',
                                flags: 'i',
                                action: 'delete',
                                deleteMessage: true
                              } as any;
                              setSettings(prev => ({
                                ...prev,
                                moderation: {
                                  ...prev.moderation,
                                  autoMod: {
                                    ...prev.moderation.autoMod,
                                    regexFilters: [...(prev.moderation.autoMod.regexFilters || []), newFilter]
                                  }
                                }
                              }));
                            }}
                          >
                            Add Filter
                          </Button>
                        </HStack>

                        <VStack spacing={4} align="stretch">
                          {(settings.moderation.autoMod.regexFilters || []).map((filter, idx) => (
                            <Card key={filter.id} variant="outline">
                              <CardBody>
                                <VStack spacing={4} align="stretch">
                                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                                    <FormControl isRequired>
                                      <FormLabel>Filter Name</FormLabel>
                                      <Input
                                        value={filter.name || ''}
                                        placeholder="profanity, invites, etc."
                                        onChange={(e) => {
                                          const newFilters = [...settings.moderation.autoMod.regexFilters];
                                          newFilters[idx].name = e.target.value;
                                          setSettings(prev => ({
                                            ...prev,
                                            moderation: {
                                              ...prev.moderation,
                                              autoMod: {
                                                ...prev.moderation.autoMod,
                                                regexFilters: newFilters
                                              }
                                            }
                                          }));
                                        }}
                                      />
                                    </FormControl>

                                    <FormControl>
                                      <FormLabel>Flags</FormLabel>
                                      <Input
                                        value={filter.flags}
                                        placeholder="i, g, m, etc."
                                        onChange={(e) => {
                                          const newFilters = [...settings.moderation.autoMod.regexFilters];
                                          newFilters[idx].flags = e.target.value;
                                          setSettings(prev => ({
                                            ...prev,
                                            moderation: {
                                              ...prev.moderation,
                                              autoMod: {
                                                ...prev.moderation.autoMod,
                                                regexFilters: newFilters
                                              }
                                            }
                                          }));
                                        }}
                                      />
                                    </FormControl>
                                  </SimpleGrid>

                                  <FormControl isRequired>
                                    <FormLabel>Regex Pattern</FormLabel>
                                    <Textarea
                                      value={filter.pattern}
                                      placeholder="Enter full regex pattern here"
                                      onChange={(e) => {
                                        const newFilters = [...settings.moderation.autoMod.regexFilters];
                                        newFilters[idx].pattern = e.target.value;
                                        setSettings(prev => ({
                                          ...prev,
                                          moderation: {
                                            ...prev.moderation,
                                            autoMod: {
                                              ...prev.moderation.autoMod,
                                              regexFilters: newFilters
                                            }
                                          }
                                        }));
                                      }}
                                      resize="vertical"
                                      minH="80px"
                                    />
                                  </FormControl>

                                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                                    <FormControl>
                                      <FormLabel>Action</FormLabel>
                                      <Select
                                        value={filter.action}
                                        onChange={(e) => {
                                          const newFilters = [...settings.moderation.autoMod.regexFilters];
                                          newFilters[idx].action = e.target.value;
                                          setSettings(prev => ({
                                            ...prev,
                                            moderation: {
                                              ...prev.moderation,
                                              autoMod: {
                                                ...prev.moderation.autoMod,
                                                regexFilters: newFilters
                                              }
                                            }
                                          }));
                                        }}
                                      >
                                        <option value="delete">Delete Message</option>
                                        <option value="warn">Warn</option>
                                        <option value="timeout">Timeout</option>
                                        <option value="ban">Ban</option>
                                      </Select>
                                    </FormControl>

                                    <FormControl display="flex" alignItems="center">
                                      <FormLabel flex="1">Delete Message</FormLabel>
                                      <Switch
                                        colorScheme="red"
                                        isChecked={filter.deleteMessage}
                                        onChange={(e) => {
                                          const newFilters = [...settings.moderation.autoMod.regexFilters];
                                          newFilters[idx].deleteMessage = e.target.checked;
                                          setSettings(prev => ({
                                            ...prev,
                                            moderation: {
                                              ...prev.moderation,
                                              autoMod: {
                                                ...prev.moderation.autoMod,
                                                regexFilters: newFilters
                                              }
                                            }
                                          }));
                                        }}
                                      />
                                    </FormControl>
                                  </SimpleGrid>

                                  <Button
                                    size="sm"
                                    colorScheme="red"
                                    variant="ghost"
                                    leftIcon={<Icon as={FiTrash2} />}
                                    onClick={() => {
                                      const newFilters = settings.moderation.autoMod.regexFilters.filter((_, i) => i !== idx);
                                      setSettings(prev => ({
                                        ...prev,
                                        moderation: {
                                          ...prev.moderation,
                                          autoMod: {
                                            ...prev.moderation.autoMod,
                                            regexFilters: newFilters
                                          }
                                        }
                                      }));
                                    }}
                                  >
                                    Remove Filter
                                  </Button>
                                </VStack>
                              </CardBody>
                            </Card>
                          ))}
                        </VStack>
                      </VStack>
                    </CardBody>
                  </Card>

                  <Alert status="info" borderRadius="md">
                    <AlertIcon />
                    <AlertDescription>
                      Existing built-in filters (profanity, links, caps, etc.) will be exposed in future updates.
                    </AlertDescription>
                  </Alert>
                </VStack>
              </TabPanel>

              {/* Punishments Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {/* Warnings */}
                  <Card>
                    <CardHeader>
                      <HStack justify="space-between">
                        <Heading size="md">
                          <Icon as={FiUserX} mr={2} color="orange.400" />
                          Warnings & Escalation
                        </Heading>
                        <Switch
                          colorScheme="red"
                          size="lg"
                          isChecked={settings.moderation.punishments.warnings.enabled}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            moderation: {
                              ...prev.moderation,
                              punishments: {
                                ...prev.moderation.punishments,
                                warnings: {
                                  ...prev.moderation.punishments.warnings,
                                  enabled: e.target.checked
                                }
                              }
                            }
                          }))}
                        />
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        <FormControl>
                          <FormLabel>Max Warnings</FormLabel>
                          <NumberInput
                            value={settings.moderation.punishments.warnings.maxWarnings}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev,
                              moderation: {
                                ...prev.moderation,
                                punishments: {
                                  ...prev.moderation.punishments,
                                  warnings: {
                                    ...prev.moderation.punishments.warnings,
                                    maxWarnings: val || 3
                                  }
                                }
                              }
                            }))}
                            min={1} max={10}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                        </FormControl>

                        <FormControl>
                          <FormLabel>Escalation Action</FormLabel>
                          <Select
                            value={settings.moderation.punishments.warnings.escalationAction}
                            onChange={(e) => setSettings(prev => ({
                              ...prev,
                              moderation: {
                                ...prev.moderation,
                                punishments: {
                                  ...prev.moderation.punishments,
                                  warnings: {
                                    ...prev.moderation.punishments.warnings,
                                    escalationAction: e.target.value
                                  }
                                }
                              }
                            }))}
                          >
                            <option value="timeout">Timeout</option>
                            <option value="kick">Kick</option>
                            <option value="ban">Ban</option>
                          </Select>
                        </FormControl>

                        <FormControl display="flex" alignItems="center">
                          <FormLabel flex="1">Send DM</FormLabel>
                          <Switch
                            colorScheme="red"
                            isChecked={settings.moderation.punishments.warnings.dmUser}
                            onChange={(e) => setSettings(prev => ({
                              ...prev,
                              moderation: {
                                ...prev.moderation,
                                punishments: {
                                  ...prev.moderation.punishments,
                                  warnings: {
                                    ...prev.moderation.punishments.warnings,
                                    dmUser: e.target.checked
                                  }
                                }
                              }
                            }))}
                          />
                        </FormControl>
                      </SimpleGrid>
                    </CardBody>
                  </Card>

                  {/* Timeouts */}
                  <Card>
                    <CardHeader>
                      <HStack justify="space-between">
                        <Heading size="md">
                          <Icon as={FiClock} mr={2} color="blue.400" />
                          Timeouts
                        </Heading>
                        <Switch
                          colorScheme="red"
                          size="lg"
                          isChecked={true}
                          isDisabled
                        />
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        <FormControl>
                          <FormLabel>Default Duration (minutes)</FormLabel>
                          <NumberInput
                            value={settings.moderation.punishments.timeouts.defaultDuration / 60000}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev,
                              moderation: {
                                ...prev.moderation,
                                punishments: {
                                  ...prev.moderation.punishments,
                                  timeouts: {
                                    ...prev.moderation.punishments.timeouts,
                                    defaultDuration: (val || 10) * 60000
                                  }
                                }
                              }
                            }))}
                            min={1} max={1440}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                        </FormControl>

                        <FormControl>
                          <FormLabel>Max Duration (hours)</FormLabel>
                          <NumberInput
                            value={settings.moderation.punishments.timeouts.maxDuration / 3600000}
                            onChange={(_, val) => setSettings(prev => ({
                              ...prev,
                              moderation: {
                                ...prev.moderation,
                                punishments: {
                                  ...prev.moderation.punishments,
                                  timeouts: {
                                    ...prev.moderation.punishments.timeouts,
                                    maxDuration: (val || 24) * 3600000
                                  }
                                }
                              }
                            }))}
                            min={1} max={672}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                        </FormControl>

                        <FormControl display="flex" alignItems="center">
                          <FormLabel flex="1">Send DM</FormLabel>
                          <Switch
                            colorScheme="red"
                            isChecked={settings.moderation.punishments.timeouts.dmUser}
                            onChange={(e) => setSettings(prev => ({
                              ...prev,
                              moderation: {
                                ...prev.moderation,
                                punishments: {
                                  ...prev.moderation.punishments,
                                  timeouts: {
                                    ...prev.moderation.punishments.timeouts,
                                    dmUser: e.target.checked
                                  }
                                }
                              }
                            }))}
                          />
                        </FormControl>
                      </SimpleGrid>
                    </CardBody>
                  </Card>

                  {/* Kicks & Bans quick toggles */}
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                    <Card>
                      <CardHeader>
                        <Heading size="sm"><Icon as={FiUserX} mr={2} color="purple.400" /> Kick Settings</Heading>
                      </CardHeader>
                      <CardBody>
                        <FormControl display="flex" alignItems="center">
                          <FormLabel flex="1">Send DM</FormLabel>
                          <Switch
                            colorScheme="red"
                            isChecked={settings.moderation.punishments.kicks.dmUser}
                            onChange={(e) => setSettings(prev => ({
                              ...prev,
                              moderation: {
                                ...prev.moderation,
                                punishments: {
                                  ...prev.moderation.punishments,
                                  kicks: {
                                    ...prev.moderation.punishments.kicks,
                                    dmUser: e.target.checked
                                  }
                                }
                              }
                            }))}
                          />
                        </FormControl>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm"><Icon as={FiUserX} mr={2} color="red.400" /> Ban Settings</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={4} align="stretch">
                          <FormControl display="flex" alignItems="center">
                            <FormLabel flex="1">Send DM</FormLabel>
                            <Switch
                              colorScheme="red"
                              isChecked={settings.moderation.punishments.bans.dmUser}
                              onChange={(e) => setSettings(prev => ({
                                ...prev,
                                moderation: {
                                  ...prev.moderation,
                                  punishments: {
                                    ...prev.moderation.punishments,
                                    bans: {
                                      ...prev.moderation.punishments.bans,
                                      dmUser: e.target.checked
                                    }
                                  }
                                }
                              }))}
                            />
                          </FormControl>

                          <FormControl>
                            <FormLabel>Delete Message Days</FormLabel>
                            <NumberInput
                              value={settings.moderation.punishments.bans.deleteMessageDays}
                              onChange={(_, val) => setSettings(prev => ({
                                ...prev,
                                moderation: {
                                  ...prev.moderation,
                                  punishments: {
                                    ...prev.moderation.punishments,
                                    bans: {
                                      ...prev.moderation.punishments.bans,
                                      deleteMessageDays: val || 0
                                    }
                                  }
                                }
                              }))}
                              min={0} max={7}
                            >
                              <NumberInputField />
                              <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                              </NumberInputStepper>
                            </NumberInput>
                          </FormControl>
                        </VStack>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </VStack>
              </TabPanel>

              {/* Logging Tab */}
              <TabPanel>
                <Card>
                  <CardHeader>
                    <HStack justify="space-between">
                      <Heading size="md">
                        <Icon as={FiLock} mr={2} color="blue.400" />
                        Moderation Logging
                      </Heading>
                      <Switch 
                        colorScheme="red"
                        size="lg"
                        isChecked={settings.moderation.logging.enabled}
                        onChange={(e) => setSettings(prev => ({
                          ...prev, 
                          moderation: {
                            ...prev.moderation,
                            logging: { 
                              ...prev.moderation.logging, 
                              enabled: e.target.checked 
                            }
                          }
                        }))}
                      />
                    </HStack>
                  </CardHeader>
                  <CardBody>
                    <VStack spacing={4} align="stretch">
                      <FormControl>
                        <FormLabel>Log Channel</FormLabel>
                        <Select 
                          placeholder="Select log channel"
                          value={settings.moderation.logging.channelId || ''}
                          onChange={(e) => setSettings(prev => ({
                            ...prev, 
                            moderation: {
                              ...prev.moderation,
                              logging: { 
                                ...prev.moderation.logging, 
                                channelId: e.target.value 
                              }
                            }
                          }))}
                        >
                          {textChannels && textChannels.length > 0 ? (
                            textChannels.map(channel => (
                              <option key={channel.id} value={channel.id}>
                                #{channel.name}
                              </option>
                            ))
                          ) : (
                            <option disabled>No channels available</option>
                          )}
                        </Select>
                      </FormControl>

                      <Text fontWeight="bold" mt={4}>Log Events</Text>
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        {[
                          { key: 'logRaidProtection', label: 'Raid Protection' },
                          { key: 'logSpamPrevention', label: 'Spam Prevention' },
                          { key: 'logAutoMod', label: 'Auto-Moderation' },
                          { key: 'logManualActions', label: 'Manual Actions' },
                          { key: 'logWarnings', label: 'Warnings' },
                          { key: 'logTimeouts', label: 'Timeouts' },
                          { key: 'logKicks', label: 'Kicks' },
                          { key: 'logBans', label: 'Bans' }
                        ].map(({ key, label }) => (
                          <FormControl key={key} display="flex" alignItems="center">
                            <FormLabel flex="1">{label}</FormLabel>
                            <Switch 
                              colorScheme="red"
                              isChecked={settings.moderation.logging[key]}
                              onChange={(e) => setSettings(prev => ({
                                ...prev, 
                                moderation: {
                                  ...prev.moderation,
                                  logging: { 
                                    ...prev.moderation.logging, 
                                    [key]: e.target.checked 
                                  }
                                }
                              }))}
                              sx={{
                                'span.chakra-switch__track': {
                                  bg: settings.moderation.logging[key] ? 'red.600' : 'gray.300',
                                  _dark: {
                                    bg: settings.moderation.logging[key] ? 'red.800' : 'gray.600'
                                  }
                                },
                                'span.chakra-switch__thumb': {
                                  bg: settings.moderation.logging[key] ? 'red.100' : 'gray.100'
                                }
                              }}
                            />
                          </FormControl>
                        ))}
                      </SimpleGrid>

                      <Divider />

                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        <FormControl display="flex" alignItems="center">
                          <FormLabel flex="1">Use Embed Logs</FormLabel>
                          <Switch 
                            colorScheme="red"
                            isChecked={settings.moderation.logging.embedLogs}
                            onChange={(e) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                logging: { 
                                  ...prev.moderation.logging, 
                                  embedLogs: e.target.checked 
                                }
                              }
                            }))}
                          />
                        </FormControl>

                        <FormControl display="flex" alignItems="center">
                          <FormLabel flex="1">Detailed Logs</FormLabel>
                          <Switch 
                            colorScheme="red"
                            isChecked={settings.moderation.logging.detailedLogs}
                            onChange={(e) => setSettings(prev => ({
                              ...prev, 
                              moderation: {
                                ...prev.moderation,
                                logging: { 
                                  ...prev.moderation.logging, 
                                  detailedLogs: e.target.checked 
                                }
                              }
                            }))}
                          />
                        </FormControl>
                      </SimpleGrid>
                    </VStack>
                  </CardBody>
                </Card>
              </TabPanel>

              {/* Appeals Tab - Placeholder for now */}
              <TabPanel>
                <Alert status="info">
                  <AlertIcon />
                  <AlertDescription>
                    Appeals system will be implemented in the next phase.
                  </AlertDescription>
                </Alert>
              </TabPanel>

              {/* Bypass Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Alert status="info">
                    <AlertIcon />
                    <Box>
                      <AlertDescription>
                        Configure bypass rules to exempt specific roles from moderation actions. Higher priority rules are applied first.
                      </AlertDescription>
                    </Box>
                  </Alert>

                  <Card>
                    <CardHeader>
                      <HStack justify="space-between">
                        <Heading size="md">
                          <Icon as={FiUsers} mr={2} color="blue.400" />
                          Bypass System
                        </Heading>
                        <Switch 
                          colorScheme="red"
                          size="lg"
                          isChecked={settings.moderation.bypass.enabled}
                          onChange={(e) => setSettings(prev => ({
                            ...prev, 
                            moderation: {
                              ...prev.moderation,
                              bypass: { 
                                ...prev.moderation.bypass, 
                                enabled: e.target.checked 
                              }
                            }
                          }))}
                        />
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={4} align="stretch">
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                          <FormControl>
                            <FormLabel>Inheritance Mode</FormLabel>
                            <Select 
                              value={settings.moderation.bypass.inheritanceMode}
                              onChange={(e) => setSettings(prev => ({
                                ...prev, 
                                moderation: {
                                  ...prev.moderation,
                                  bypass: { 
                                    ...prev.moderation.bypass, 
                                    inheritanceMode: e.target.value 
                                  }
                                }
                              }))}
                            >
                              <option value="highest">Highest Priority</option>
                              <option value="additive">Additive (Combine All)</option>
                            </Select>
                            <Text fontSize="sm" color="gray.500">
                              How to handle users with multiple bypass roles
                            </Text>
                          </FormControl>

                          <FormControl display="flex" alignItems="center">
                            <FormLabel flex="1">Log Bypass Events</FormLabel>
                            <Switch 
                              colorScheme="red"
                              isChecked={settings.moderation.bypass.logBypassEvents}
                              onChange={(e) => setSettings(prev => ({
                                ...prev, 
                                moderation: {
                                  ...prev.moderation,
                                  bypass: { 
                                    ...prev.moderation.bypass, 
                                    logBypassEvents: e.target.checked 
                                  }
                                }
                              }))}
                            />
                          </FormControl>
                        </SimpleGrid>

                        <Divider />

                        <HStack justify="space-between">
                          <Heading size="sm">Bypass Rules</Heading>
                          <Button 
                            size="sm" 
                            colorScheme="blue"
                            onClick={() => {
                              const newRule = {
                                id: `custom-rule-${Date.now()}`,
                                name: 'Custom Bypass Rule',
                                description: 'Custom bypass rule description',
                                roleIds: [],
                                bypassFeatures: [],
                                priority: 10,
                                enabled: true
                              };
                              setSettings(prev => ({
                                ...prev,
                                moderation: {
                                  ...prev.moderation,
                                  bypass: {
                                    ...prev.moderation.bypass,
                                    rules: [...prev.moderation.bypass.rules, newRule]
                                  }
                                }
                              }));
                            }}
                          >
                            Add Rule
                          </Button>
                        </HStack>

                        <VStack spacing={4} align="stretch">
                          {settings.moderation.bypass.rules.map((rule, index) => (
                            <Card key={rule.id} variant="outline">
                              <CardBody>
                                <VStack spacing={4} align="stretch">
                                  <HStack justify="space-between">
                                    <VStack align="start" spacing={0}>
                                      <Text fontWeight="bold">{rule.name}</Text>
                                      <Text fontSize="sm" color="gray.500">{rule.description}</Text>
                                    </VStack>
                                    <HStack>
                                      <Badge colorScheme="blue">Priority: {rule.priority}</Badge>
                                      <Switch 
                                        colorScheme="red"
                                        size="sm"
                                        isChecked={rule.enabled}
                                        onChange={(e) => {
                                          const newRules = [...settings.moderation.bypass.rules];
                                          newRules[index].enabled = e.target.checked;
                                          setSettings(prev => ({
                                            ...prev,
                                            moderation: {
                                              ...prev.moderation,
                                              bypass: {
                                                ...prev.moderation.bypass,
                                                rules: newRules
                                              }
                                            }
                                          }));
                                        }}
                                      />
                                      <Button 
                                        size="sm" 
                                        variant="ghost" 
                                        colorScheme="red"
                                        onClick={() => {
                                          const newRules = settings.moderation.bypass.rules.filter((_, i) => i !== index);
                                          setSettings(prev => ({
                                            ...prev,
                                            moderation: {
                                              ...prev.moderation,
                                              bypass: {
                                                ...prev.moderation.bypass,
                                                rules: newRules
                                              }
                                            }
                                          }));
                                        }}
                                      >
                                        <Icon as={FiTrash2} />
                                      </Button>
                                    </HStack>
                                  </HStack>

                                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                                    <FormControl>
                                      <FormLabel>Rule Name</FormLabel>
                                      <Input 
                                        value={rule.name}
                                        onChange={(e) => {
                                          const newRules = [...settings.moderation.bypass.rules];
                                          newRules[index].name = e.target.value;
                                          setSettings(prev => ({
                                            ...prev,
                                            moderation: {
                                              ...prev.moderation,
                                              bypass: {
                                                ...prev.moderation.bypass,
                                                rules: newRules
                                              }
                                            }
                                          }));
                                        }}
                                      />
                                    </FormControl>

                                    <FormControl>
                                      <FormLabel>Priority</FormLabel>
                                      <NumberInput 
                                        value={rule.priority}
                                        onChange={(_, val) => {
                                          const newRules = [...settings.moderation.bypass.rules];
                                          newRules[index].priority = val || 10;
                                          setSettings(prev => ({
                                            ...prev,
                                            moderation: {
                                              ...prev.moderation,
                                              bypass: {
                                                ...prev.moderation.bypass,
                                                rules: newRules
                                              }
                                            }
                                          }));
                                        }}
                                        min={1} max={1000}
                                      >
                                        <NumberInputField />
                                        <NumberInputStepper>
                                          <NumberIncrementStepper />
                                          <NumberDecrementStepper />
                                        </NumberInputStepper>
                                      </NumberInput>
                                    </FormControl>
                                  </SimpleGrid>

                                  <FormControl>
                                    <FormLabel>Description</FormLabel>
                                    <Textarea 
                                      value={rule.description}
                                      onChange={(e) => {
                                        const newRules = [...settings.moderation.bypass.rules];
                                        newRules[index].description = e.target.value;
                                        setSettings(prev => ({
                                          ...prev,
                                          moderation: {
                                            ...prev.moderation,
                                            bypass: {
                                              ...prev.moderation.bypass,
                                              rules: newRules
                                            }
                                          }
                                        }));
                                      }}
                                      resize="vertical"
                                      minH="60px"
                                    />
                                  </FormControl>

                                  <FormControl>
                                    <FormLabel>Roles with Bypass</FormLabel>
                                    <CheckboxGroup 
                                      value={rule.roleIds}
                                      onChange={(values) => {
                                        const newRules = [...settings.moderation.bypass.rules];
                                        newRules[index].roleIds = values as string[];
                                        setSettings(prev => ({
                                          ...prev,
                                          moderation: {
                                            ...prev.moderation,
                                            bypass: {
                                              ...prev.moderation.bypass,
                                              rules: newRules
                                            }
                                          }
                                        }));
                                      }}
                                    >
                                      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={2}>
                                        {roles.map(role => (
                                          <Checkbox key={role.id} value={role.id}>
                                            <HStack>
                                              <Box 
                                                w={3} 
                                                h={3} 
                                                borderRadius="full" 
                                                bg={role.color ? `#${role.color.toString(16).padStart(6, '0')}` : 'gray.400'}
                                              />
                                              <Text>{role.name}</Text>
                                            </HStack>
                                          </Checkbox>
                                        ))}
                                      </SimpleGrid>
                                    </CheckboxGroup>
                                  </FormControl>

                                  <FormControl>
                                    <FormLabel>Bypass Features</FormLabel>
                                    <CheckboxGroup 
                                      value={rule.bypassFeatures}
                                      onChange={(values) => {
                                        const newRules = [...settings.moderation.bypass.rules];
                                        newRules[index].bypassFeatures = values as string[];
                                        setSettings(prev => ({
                                          ...prev,
                                          moderation: {
                                            ...prev.moderation,
                                            bypass: {
                                              ...prev.moderation.bypass,
                                              rules: newRules
                                            }
                                          }
                                        }));
                                      }}
                                    >
                                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={2}>
                                        <Checkbox value="raidProtection">
                                          <HStack>
                                            <Icon as={FiShield} color="red.400" />
                                            <Text>Raid Protection</Text>
                                          </HStack>
                                        </Checkbox>
                                        <Checkbox value="spamPrevention">
                                          <HStack>
                                            <Icon as={FiMessageSquare} color="orange.400" />
                                            <Text>Spam Prevention</Text>
                                          </HStack>
                                        </Checkbox>
                                        <Checkbox value="autoMod">
                                          <HStack>
                                            <Icon as={FiFilter} color="blue.400" />
                                            <Text>Auto-Moderation</Text>
                                          </HStack>
                                        </Checkbox>
                                        <Checkbox value="punishments">
                                          <HStack>
                                            <Icon as={FiUserX} color="purple.400" />
                                            <Text>Manual Punishments</Text>
                                          </HStack>
                                        </Checkbox>
                                      </SimpleGrid>
                                    </CheckboxGroup>
                                  </FormControl>
                                </VStack>
                              </CardBody>
                            </Card>
                          ))}
                        </VStack>
                      </VStack>
                    </CardBody>
                  </Card>
                </VStack>
              </TabPanel>

              {/* Placeholders Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Alert status="info">
                    <AlertIcon />
                    <Box>
                      <AlertDescription>
                        Use these placeholders in your embed messages. They will be automatically replaced with actual values when notifications are sent.
                      </AlertDescription>
                    </Box>
                  </Alert>

                  <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                    {/* User Placeholders */}
                    <Card>
                      <CardHeader>
                        <Heading size="md" color="blue.500">👤 User Placeholders</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={3} align="stretch">
                          {[
                            { name: '{user}', description: 'Mentions the user, e.g., @Username' },
                            { name: '{username}', description: 'The user\'s name, e.g., Username' },
                            { name: '{userTag}', description: 'The user\'s tag, e.g., Username#1234' },
                            { name: '{userId}', description: 'The user\'s ID, e.g., 123456789012345678' },
                            { name: '{userBanner}', description: 'URL of the user\'s banner (if they have one)' },
                            { name: '{user-createdAt}', description: 'The date the user\'s account was created' },
                            { name: '{UserCreation}', description: 'How long ago the user\'s account was created (e.g., "2 years ago")' },
                          ].map((placeholder, index) => (
                            <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="blue.50" _dark={{ bg: "blue.900" }}>
                              <HStack justify="space-between">
                                <VStack align="start" spacing={1}>
                                  <Text fontFamily="mono" fontWeight="bold" color="blue.600" _dark={{ color: "blue.300" }}>
                                    {placeholder.name}
                                  </Text>
                                  <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                    {placeholder.description}
                                  </Text>
                                </VStack>
                                <Tooltip label="Copy to clipboard">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => {
                                      navigator.clipboard.writeText(placeholder.name);
                                    }}
                                  >
                                    <Icon as={FiCopy} />
                                  </Button>
                                </Tooltip>
                              </HStack>
                            </Box>
                          ))}
                        </VStack>
                      </CardBody>
                    </Card>

                    {/* Server Placeholders */}
                    <Card>
                      <CardHeader>
                        <Heading size="md" color="green.500">🏰 Server Placeholders</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={3} align="stretch">
                          {[
                            { name: '{guild} / {server}', description: 'The server\'s name' },
                            { name: '{guildIcon}', description: 'URL of the server\'s icon' },
                            { name: '{memberCount}', description: 'Total members with ordinal suffix, e.g., "100th"' },
                            { name: '{memberCountNumeric}', description: 'Total members as a number, e.g., "100"' },
                          ].map((placeholder, index) => (
                            <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="green.50" _dark={{ bg: "green.900" }}>
                              <HStack justify="space-between">
                                <VStack align="start" spacing={1}>
                                  <Text fontFamily="mono" fontWeight="bold" color="green.600" _dark={{ color: "green.300" }}>
                                    {placeholder.name}
                                  </Text>
                                  <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                    {placeholder.description}
                                  </Text>
                                </VStack>
                                <Tooltip label="Copy to clipboard">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => {
                                      navigator.clipboard.writeText(placeholder.name.split(' / ')[0]);
                                    }}
                                  >
                                    <Icon as={FiCopy} />
                                  </Button>
                                </Tooltip>
                              </HStack>
                            </Box>
                          ))}
                        </VStack>
                      </CardBody>
                    </Card>

                    {/* Moderation Placeholders */}
                    <Card>
                      <CardHeader>
                        <Heading size="md" color="red.500">🛡️ Moderation Placeholders</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={3} align="stretch">
                          {[
                            { name: '{moderator}', description: 'Mentions the moderator who took action' },
                            { name: '{moderatorName}', description: 'The moderator\'s username' },
                            { name: '{action}', description: 'The action taken (ban, kick, timeout, etc.)' },
                            { name: '{reason}', description: 'The reason for the action' },
                            { name: '{duration}', description: 'Duration of timeout/ban (human readable)' },
                            { name: '{expiresAt}', description: 'When the punishment expires' },
                            { name: '{channel}', description: 'The channel where the violation occurred' },
                          ].map((placeholder, index) => (
                            <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="red.50" _dark={{ bg: "red.900" }}>
                              <HStack justify="space-between">
                                <VStack align="start" spacing={1}>
                                  <Text fontFamily="mono" fontWeight="bold" color="red.600" _dark={{ color: "red.300" }}>
                                    {placeholder.name}
                                  </Text>
                                  <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                    {placeholder.description}
                                  </Text>
                                </VStack>
                                <Tooltip label="Copy to clipboard">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => {
                                      navigator.clipboard.writeText(placeholder.name);
                                    }}
                                  >
                                    <Icon as={FiCopy} />
                                  </Button>
                                </Tooltip>
                              </HStack>
                            </Box>
                          ))}
                        </VStack>
                      </CardBody>
                    </Card>

                    {/* Statistics Placeholders */}
                    <Card>
                      <CardHeader>
                        <Heading size="md" color="orange.500">📊 Statistics Placeholders</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={3} align="stretch">
                          {[
                            { name: '{threshold}', description: 'The threshold that was exceeded' },
                            { name: '{timeWindow}', description: 'The time window in milliseconds' },
                            { name: '{messageCount}', description: 'Number of messages sent' },
                            { name: '{warningCount}', description: 'Current warning count' },
                            { name: '{maxWarnings}', description: 'Maximum warnings allowed' },
                            { name: '{timestamp}', description: 'Current date and time' },
                          ].map((placeholder, index) => (
                            <Box key={index} p={3} borderWidth={1} borderRadius="md" bg="orange.50" _dark={{ bg: "orange.900" }}>
                              <HStack justify="space-between">
                                <VStack align="start" spacing={1}>
                                  <Text fontFamily="mono" fontWeight="bold" color="orange.600" _dark={{ color: "orange.300" }}>
                                    {placeholder.name}
                                  </Text>
                                  <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                    {placeholder.description}
                                  </Text>
                                </VStack>
                                <Tooltip label="Copy to clipboard">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => {
                                      navigator.clipboard.writeText(placeholder.name);
                                    }}
                                  >
                                    <Icon as={FiCopy} />
                                  </Button>
                                </Tooltip>
                              </HStack>
                            </Box>
                          ))}
                        </VStack>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
        <ModalFooter>
          <HStack>
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="red" onClick={handleSave} isLoading={loading}>
              Save Settings
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ModerationSystemDialog; 