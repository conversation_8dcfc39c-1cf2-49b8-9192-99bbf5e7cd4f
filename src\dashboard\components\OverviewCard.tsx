import React from 'react';
import Link from 'next/link';
import { useTheme } from '../contexts/ThemeContext';

export interface OverviewCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  color: string;
  gradient?: {
    from: string;
    to: string;
  };
  accentColor?: string;
  disabled?: boolean;
  experimental?: boolean;
}

export const OverviewCard: React.FC<OverviewCardProps> = ({ 
  title, 
  description, 
  icon: Icon, 
  href, 
  color,
  gradient,
  accentColor,
  disabled = false,
  experimental = false
}) => {
  const { currentScheme } = useTheme();
  const isClickable = href && href !== '#' && !disabled;
  
  // Dynamic class generation functions
  const getBackgroundStyle = () => {
    if (gradient) {
      return `bg-gradient-to-r from-[${gradient.from}] to-[${gradient.to}]`;
    }
    return 'bg-gray-800';
  };

  const getCardStyles = () => {
    const baseStyles = `
      relative flex flex-col justify-start items-start 
      px-10 py-5 h-[140px] w-full 
      overflow-hidden rounded-lg border 
      transition-all duration-300
      min-w-[360px] sm:min-w-[280px] md:min-w-[320px] lg:min-w-[360px]
    `;

    const backgroundStyles = getBackgroundStyle();
    const borderStyles = disabled 
      ? 'border-white/10 opacity-60 cursor-not-allowed' 
      : 'border-white/20 hover:border-blue-400 hover:translate-y-[-3px]';
    
    const cursorStyles = isClickable ? 'cursor-pointer' : 'cursor-default';

    return `${baseStyles} ${backgroundStyles} ${borderStyles} ${cursorStyles}`;
  };

  const getExperimentalOverlay = () => {
    return experimental 
      ? 'before:absolute before:inset-0 before:bg-repeating-diagonal before:opacity-50 hover:before:opacity-70' 
      : '';
  };

  const getIconStyles = () => {
    const baseIconStyles = `w-6 h-6 ${experimental ? 'drop-shadow-[0_0_2px_currentColor]' : ''}`;
    const colorStyles = accentColor 
      ? `text-[${accentColor}]` 
      : `text-${color}-300`;
    
    return `${baseIconStyles} ${colorStyles}`;
  };

  const cardContent = (
    <div className={`${getCardStyles()} ${getExperimentalOverlay()}`}>
      <div className="flex flex-col space-y-4 w-full h-full relative z-10">
        <div className="flex items-center space-x-3">
          <Icon 
            className={getIconStyles()} 
          />
          <h2 className="text-md font-bold text-white truncate">
            {title}
          </h2>
        </div>
        <p 
          className={`
            text-sm leading-[1.4] line-clamp-3 
            ${disabled ? 'text-gray-500' : 'text-gray-300'}
          `}
        >
          {description}
        </p>
      </div>
    </div>
  );

  if (isClickable) {
    return (
      <Link href={href}>
        {cardContent}
      </Link>
    );
  }

  return cardContent;
};

 