// Simple Mongo helper for API routes
import { dashboardConfig } from '../core/config';
import { MongoClient, Db } from 'mongodb';

let cachedClient: MongoClient | null = null;
let cachedDb: Db | null = null;

export async function getDb(): Promise<Db> {
  if (cachedDb && cachedClient) {
    // Check if connection is still alive
    try {
      await cachedDb.admin().ping();
      return cachedDb;
    } catch (error) {
      // Connection is dead, reset cache
      cachedClient = null;
      cachedDb = null;
    }
  }

  const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
  const dbName = dashboardConfig.database?.name || 'discord_bot';

  if (!cachedClient) {
    // Use optimized connection options to reduce connection creation
    const options = {
      maxPoolSize: 10,
      minPoolSize: 2,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 10000,
      retryWrites: true,
      retryReads: true,
      // Reduce connection logging
      monitorCommands: false,
      ...dashboardConfig.database?.options
    };

    cachedClient = await MongoClient.connect(mongoUrl, options);
  }
  
  cachedDb = cachedClient.db(dbName);
  return cachedDb;
} 