import path from 'path';

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  pageExtensions: ['tsx', 'ts', 'js', 'jsx'],
  experimental: {
    // Add any necessary experimental features
  },
  webpack: (config, { isServer }) => {
    config.resolve.alias['@'] = path.resolve(process.cwd(), 'src/dashboard');
    return config;
  },
  // Specify the project root and pages directory
  distDir: '.next',
  configOrigin: 'src/dashboard',
  // Explicitly set the source directory
  sourceDir: path.resolve(process.cwd(), 'src/dashboard')
};

export default nextConfig;
