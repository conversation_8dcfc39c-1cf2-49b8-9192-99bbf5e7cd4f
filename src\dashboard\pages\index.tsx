import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './api/auth/[...nextauth]';
import { useSession } from 'next-auth/react';
import { useEffect } from 'react';

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  console.log('Index page server-side props');
  const session = await getServerSession(ctx.req, ctx.res, authOptions);

  console.log('Session on index page:', JSON.stringify(session, null, 2));

  // Temporarily remove redirects to diagnose
  return { props: { session } };
};

export default function Home() {
  const { data: session, status } = useSession();

  useEffect(() => {
    console.log('Client-side session:', session);
    console.log('Session status:', status);
  }, [session, status]);

  return (
    <div>
      <h1>Dashboard Home</h1>
      {status === 'loading' && <p>Loading...</p>}
      {status === 'unauthenticated' && <p>Not logged in</p>}
      {status === 'authenticated' && (
        <div>
          <p>Logged in as: {session?.user?.name}</p>
          <pre>{JSON.stringify(session, null, 2)}</pre>
        </div>
      )}
    </div>
  );
} 