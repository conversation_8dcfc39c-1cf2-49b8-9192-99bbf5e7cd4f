declare module 'react-icons/fi' {
  import { IconType } from 'react-icons';
  export const FiServer: IconType;
  export const FiSettings: IconType;
  export const FiUsers: IconType;
  export const FiPackage: IconType;
  export const FiHelpCircle: IconType;
  export const FiMonitor: IconType;
  export const FiHome: IconType;
  export const FiDatabase: IconType;
  export const FiActivity: IconType;
  export const FiBox: IconType;
  export const FiCommand: IconType;
  export const FiChevronDown: IconType;
  export const FiAlertCircle: IconType;
  export const FiAlertTriangle: IconType;
  export const FiShield: IconType;
  export const FiTrash2: IconType;
  export const FiLock: IconType;
  export const FiMessageSquare: IconType;
  export const FiUserX: IconType;
  export const FiFlag: IconType;
  export const FiLink: IconType;
  export const FiVolume2: IconType;
  export const FiCopy: IconType;
  export const FiFilter: IconType;
  export const FiClock: IconType;
  export const FiBell: IconType;
  export const FiCheck: IconType;
  export const FiCalendarAlt: IconType;
  export const FiX: IconType;
  export const FiTrendingUp: IconType;
  export const FiMessageCircle: IconType;
}

declare module 'react-icons/fa' {
  import { IconType } from 'react-icons';
  export const FaFileAlt: IconType;
  export const FaFlask: IconType;
  export const FaQuoteLeft: IconType;
  export const FaQuoteRight: IconType;
  export const FaRobot: IconType;
} 