// @ts-nocheck
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  VStack,
  HStack,
  Icon,
  useToast,
  Card,
  CardHeader,
  CardBody,
  Button,
  Input,
  Select,
  Switch,
  FormControl,
  FormLabel,
  Badge,
  Divider,
  Skeleton,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Tooltip,
  Checkbox,
  Flex,
  RadioGroup,
  Radio,
  Spinner,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
} from '@chakra-ui/react';
import Layout from '../../components/Layout';
import useGuildInfo from '../../hooks/useGuildInfo';
import { useState, useEffect, useCallback, useRef, Suspense } from 'react';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { FiSettings, FiUsers, FiHash, FiEdit2, FiSave, FiTrash2, FiPlus, FiMessageSquare, FiVolume2, FiFolderPlus, FiRadio, FiLock, FiMessageCircle, FiTrash, FiServer, FiZap, FiTool } from 'react-icons/fi';
import { FaPalette } from 'react-icons/fa';
import dynamic from 'next/dynamic';
import { useTheme, COLOR_SCHEMES } from '../../contexts/ThemeContext';

// Dynamic imports for heavy components
const CreateChannelDialog = dynamic(() => import('../../components/CreateChannelDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const EditChannelDialog = dynamic(() => import('../../components/EditChannelDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const EditRoleDialog = dynamic(() => import('../../components/EditRoleDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const ColorBuilder = dynamic(() => import('../../components/ColorBuilder'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const CreateRoleDialog = dynamic(() => import('../../components/CreateRoleDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const WelcomeSystemDialog = dynamic(() => import('../../components/WelcomeSystemDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

// Dynamic import for Moderation System Dialog
const ModerationSystemDialog = dynamic(() => import('../../components/ModerationSystemDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

// Rate limiting constants
const RATE_LIMIT_MS = 2000; // 2 seconds between operations
const BULK_RATE_LIMIT_MS = 5000; // 5 seconds between bulk operations

// Custom hook for rate limiting
function useRateLimit(delay = RATE_LIMIT_MS) {
  const [isRateLimited, setIsRateLimited] = useState(false);
  const timeoutRef = useRef(null);

  const resetRateLimit = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsRateLimited(true);
    timeoutRef.current = setTimeout(() => {
      setIsRateLimited(false);
    }, delay);
  }, [delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [isRateLimited, resetRateLimit];
}

interface Role {
  id: string;
  name: string;
  color: number;
  position: number;
  permissions: string;
}

interface Channel {
  id: string;
  name: string;
  type: number;
  parent_id?: string;
  position: number;
  topic?: string;
  nsfw?: boolean;
  rate_limit_per_user?: number;
  bitrate?: number;
  user_limit?: number;
}

interface Activity {
  type: 'PLAYING' | 'STREAMING' | 'LISTENING' | 'WATCHING' | 'COMPETING';
  name: string;
}

interface GuildSettings {
  prefix: string;
  botName: string;
  guildName: string;
  guildId: string;
  guildIcon: string | null;
  activities: Activity[];
  activityRotationInterval: number; // in seconds
}

const CHANNEL_TYPE_CONFIG = {
  0:  { icon: FiMessageSquare, color: 'blue',   label: 'Text' },
  2:  { icon: FiVolume2,      color: 'green',  label: 'Voice' },
  4:  { icon: FiFolderPlus,   color: 'purple', label: 'Category' },
  5:  { icon: FiRadio,        color: 'orange', label: 'Announcement' },
  11: { icon: FiMessageCircle,color: 'cyan',   label: 'Public Thread' },
  12: { icon: FiLock,         color: 'pink',   label: 'Private Thread' },
  13: { icon: FiHash,         color: 'teal',   label: 'Stage Voice'},
  15: { icon: FiHash,         color: 'gray',   label: 'Forum'}
};

const PERMISSION_BADGES = {
  ADMINISTRATOR: { color: 'red', label: 'Admin' },
  MANAGE_GUILD: { color: 'orange', label: 'Manage Server' },
  MANAGE_ROLES: { color: 'yellow', label: 'Manage Roles' },
  MANAGE_CHANNELS: { color: 'green', label: 'Manage Channels' },
  KICK_MEMBERS: { color: 'purple', label: 'Kick' },
  BAN_MEMBERS: { color: 'pink', label: 'Ban' },
  MANAGE_MESSAGES: { color: 'blue', label: 'Manage Messages' },
  MENTION_EVERYONE: { color: 'cyan', label: 'Mention @everyone' },
};

// Add this helper map and function after PERMISSION_BADGES constant
const PERMISSION_FLAG_BITS: Record<keyof typeof PERMISSION_BADGES, bigint> = {
  ADMINISTRATOR: 1n << 3n,
  MANAGE_GUILD: 1n << 5n,
  MANAGE_ROLES: 1n << 28n,
  MANAGE_CHANNELS: 1n << 4n,
  KICK_MEMBERS: 1n << 1n,
  BAN_MEMBERS: 1n << 2n,
  MANAGE_MESSAGES: 1n << 13n,
  MENTION_EVERYONE: 1n << 17n,
};

function decodePermissions(bitfield: string | number): (keyof typeof PERMISSION_BADGES)[] {
  if (!bitfield) return [];
  if (Array.isArray(bitfield)) return bitfield as any;
  try {
    const permissions: (keyof typeof PERMISSION_BADGES)[] = [];
    const bits = BigInt(bitfield);
    
    for (const [permission, bit] of Object.entries(PERMISSION_FLAG_BITS)) {
      if ((bits & bit) === bit) {
        permissions.push(permission as keyof typeof PERMISSION_BADGES);
      }
    }
    
    return permissions;
  } catch (error) {
    console.error('Error decoding permissions:', error);
    return [];
  }
}

export default function ServerManagement() {
  const toast = useToast();
  const { displayName } = useGuildInfo();
  const [isRateLimited, resetRateLimit] = useRateLimit();
  const [isBulkRateLimited, resetBulkRateLimit] = useRateLimit(BULK_RATE_LIMIT_MS);
  const { currentScheme, setColorScheme, customSchemes, deleteCustomScheme } = useTheme();
  
  // State for guild settings
  const [guildData, setGuildData] = useState<GuildSettings>({
    prefix: '!',
    botName: 'Bot',
    guildName: '',
    guildId: '',
    guildIcon: null,
    activities: [{ type: 'PLAYING', name: 'with Discord.js' }],
    activityRotationInterval: 60
  });
  
  // State for roles and channels
  const [roles, setRoles] = useState<Role[]>([]);
  const [channels, setChannels] = useState<Channel[]>([]);
  const [loading, setLoading] = useState(true);
  const [channelsLoading, setChannelsLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [savingPresence, setSavingPresence] = useState(false);
  
  // Modal states
  const { isOpen: isCreateChannelOpen, onOpen: onCreateChannelOpen, onClose: onCreateChannelClose } = useDisclosure();
  const { isOpen: isEditChannelOpen, onOpen: onEditChannelOpen, onClose: onEditChannelClose } = useDisclosure();
  const { isOpen: isCreateRoleOpen, onOpen: onCreateRoleOpen, onClose: onCreateRoleClose } = useDisclosure();
  const { isOpen: isEditRoleOpen, onOpen: onEditRoleOpen, onClose: onEditRoleClose } = useDisclosure();
  const { isOpen: isColorBuilderOpen, onOpen: onColorBuilderOpen, onClose: onColorBuilderClose } = useDisclosure();
  const { isOpen: isWelcomeSystemOpen, onOpen: onWelcomeSystemOpen, onClose: onWelcomeSystemClose } = useDisclosure();
  const { 
    isOpen: isModerationSystemOpen, 
    onOpen: onModerationSystemOpen, 
    onClose: onModerationSystemClose 
  } = useDisclosure();
  
  // Selected items for editing
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<Channel | null>(null);

  // Bulk selection state
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [bulkDeleting, setBulkDeleting] = useState(false);
  
  // File upload state
  const [iconFile, setIconFile] = useState<File | null>(null);
  const [iconPreview, setIconPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleIconFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setIconFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setIconPreview(e.target?.result as string);
      reader.readAsDataURL(file);
    }
  };

  const uploadIcon = async () => {
    if (!iconFile) return;
    
    const formData = new FormData();
    formData.append('icon', iconFile);
    
    try {
      const response = await fetch('/api/discord/settings', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        const data = await response.json();
        setGuildData(prev => ({ ...prev, guildIcon: data.iconUrl }));
        setIconFile(null);
        setIconPreview(null);
        toast({
          title: 'Success',
          description: 'Guild icon updated successfully',
          status: 'success',
          duration: 3000
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to upload icon',
        status: 'error',
        duration: 3000
      });
    }
  };

  const fetchGuildData = async () => {
    try {
      const [guildResponse, rolesResponse, presenceResponse] = await Promise.all([
        fetch('/api/discord/guild'),
        fetch('/api/discord/roles'),
        fetch('/api/discord/presence')
      ]);

      if (guildResponse.ok) {
        const guild = await guildResponse.json();
        setGuildData(prev => ({
          ...prev,
          guildName: guild.name,
          guildId: guild.id,
          guildIcon: guild.icon,
          botName: guild.botName || prev.botName
        }));
      }

      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json();
        const arr = Array.isArray(rolesData) ? rolesData : rolesData.roles || [];
        setRoles(arr.sort((a: Role, b: Role) => b.position - a.position));
      }

      if (presenceResponse.ok) {
        const presenceData = await presenceResponse.json();
        setGuildData(prev => ({
          ...prev,
          activities: presenceData.activities || [],
          activityRotationInterval: presenceData.activityRotationInterval || 60
        }));
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch guild data',
        status: 'error',
        duration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchChannels = async () => {
    try {
      const response = await fetch('/api/discord/channels');
      if (!response.ok) {
        throw new Error('Failed to fetch channels');
      }
      const data = await response.json();
      const sortedChannels = (data || []).sort((a: Channel, b: Channel) => {
        if (a.type === 4 && b.type !== 4) return -1;
        if (a.type !== 4 && b.type === 4) return 1;
        return a.position - b.position;
      });
      setChannels(sortedChannels);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch channels',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setChannelsLoading(false);
    }
  };

  useEffect(() => {
    fetchGuildData();
    fetchChannels();
  }, []);

  const handleSettingChange = (setting: keyof GuildSettings, value: any) => {
    setGuildData(prev => ({ ...prev, [setting]: value }));
  };

  const saveSettings = async () => {
    if (saving || isRateLimited) return;
    
    setSaving(true);
    resetRateLimit();
    
    try {
      const response = await fetch('/api/discord/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(guildData)
      });
      
      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Settings saved successfully',
          status: 'success',
          duration: 3000
        });
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save settings',
        status: 'error',
        duration: 3000
      });
    } finally {
      setSaving(false);
    }
  };

  const handleRoleEdit = (role: Role) => {
    setSelectedRole(role);
    onEditRoleOpen();
  };

  const handleChannelEdit = (channel: Channel) => {
    setSelectedChannel(channel);
    onEditChannelOpen();
  };

  const handleChannelCreate = () => {
    onCreateChannelOpen();
  };

  const handleRoleCreate = () => {
    onCreateRoleOpen();
  };

  const handleChannelDelete = async (channelId: string) => {
    if (isRateLimited) return;
    
    try {
      resetRateLimit();
      const response = await fetch(`/api/discord/channels/${channelId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        await fetchChannels();
        toast({
          title: 'Success',
          description: 'Channel deleted successfully',
          status: 'success',
          duration: 3000
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete channel',
        status: 'error',
        duration: 3000
      });
    }
  };

  const getParentName = (parentId: string | undefined) => {
    if (!parentId || !channels) return '-';
    const parent = channels.find(c => c.id === parentId);
    return parent ? parent.name : '-';
  };

  // Bulk delete functions
  const handleBulkDeleteRoles = async () => {
    if (selectedRoles.length === 0 || bulkDeleting || isBulkRateLimited) return;

    setBulkDeleting(true);
    resetBulkRateLimit();

    try {
      const response = await fetch('/api/discord/roles/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ roleIds: selectedRoles }),
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: 'Success',
          description: result.message,
          status: 'success',
          duration: 5000,
        });
        setSelectedRoles([]);
        fetchGuildData(); // Refresh roles
      } else {
        throw new Error(result.error || 'Failed to delete roles');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete roles',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setBulkDeleting(false);
    }
  };

  const handleBulkDeleteChannels = async () => {
    if (selectedChannels.length === 0 || bulkDeleting || isBulkRateLimited) return;

    setBulkDeleting(true);
    resetBulkRateLimit();

    try {
      const response = await fetch('/api/discord/channels/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ channelIds: selectedChannels }),
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: 'Success',
          description: result.message,
          status: 'success',
          duration: 5000,
        });
        setSelectedChannels([]);
        fetchChannels(); // Refresh channels
      } else {
        throw new Error(result.error || 'Failed to delete channels');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete channels',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setBulkDeleting(false);
    }
  };

  // Selection helper functions
  const toggleRoleSelection = (roleId: string) => {
    setSelectedRoles(prev =>
      prev.includes(roleId)
        ? prev.filter(id => id !== roleId)
        : [...prev, roleId]
    );
  };

  const toggleChannelSelection = (channelId: string) => {
    setSelectedChannels(prev =>
      prev.includes(channelId)
        ? prev.filter(id => id !== channelId)
        : [...prev, channelId]
    );
  };

  const selectAllRoles = () => {
    const selectableRoles = roles.filter(role => role.name !== '@everyone').map(role => role.id);
    setSelectedRoles(selectedRoles.length === selectableRoles.length ? [] : selectableRoles);
  };

  const selectAllChannels = () => {
    const channelIds = channels.map(channel => channel.id);
    setSelectedChannels(selectedChannels.length === channelIds.length ? [] : channelIds);
  };

  // Presence/Activity functions
  const handleActivityChange = (index: number, field: keyof Activity, value: string) => {
    const newActivities = [...guildData.activities];
    newActivities[index] = { ...newActivities[index], [field]: value };
    setGuildData(prev => ({ ...prev, activities: newActivities }));
  };

  const addActivity = () => {
    setGuildData(prev => ({
      ...prev,
      activities: [...prev.activities, { type: 'PLAYING', name: '' }]
    }));
  };

  const removeActivity = (index: number) => {
    const newActivities = guildData.activities.filter((_, i) => i !== index);
    setGuildData(prev => ({ ...prev, activities: newActivities }));
  };

  const savePresence = async () => {
    if (savingPresence || isRateLimited) return;
    setSavingPresence(true);
    resetRateLimit();

    try {
      const response = await fetch('/api/discord/presence', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          activities: guildData.activities,
          activityRotationInterval: guildData.activityRotationInterval
        })
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Presence settings saved successfully',
          status: 'success',
          duration: 3000
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save presence settings');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error.message,
        status: 'error',
        duration: 5000
      });
    } finally {
      setSavingPresence(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <Container maxW="container.xl" py={8}>
          <VStack spacing={6}>
            <Skeleton height="60px" />
            <Skeleton height="400px" />
          </VStack>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box
            bg="rgba(255,255,255,0.08)"
            p={8}
            rounded="2xl"
            backdropFilter="blur(10px)"
            border="2px solid"
            borderColor="blue.400"
            boxShadow="0 0 15px rgba(66, 153, 225, 0.4)"
          >
            <HStack justify="space-between" align="center">
              <Box>
                <Heading
                  size="xl"
                  bgGradient="linear(to-r, blue.300, purple.400)"
                  bgClip="text"
                >
                  <Icon as={FiServer} mr={3} />
                  Server Management
                </Heading>
                <Text color="gray.300" mt={2}>
                  Comprehensive management for {displayName || guildData.guildName}
                </Text>
              </Box>
              <Button
                leftIcon={<FiSave />}
                colorScheme="blue"
                onClick={saveSettings}
                isLoading={saving}
                isDisabled={isRateLimited}
                size="lg"
              >
                Save Settings
              </Button>
            </HStack>
          </Box>

          {/* Main Content Tabs */}
          <Tabs colorScheme="blue" isLazy>
            <TabList>
              <Tab>
                <Icon as={FiSettings} mr={2} />
                General Settings
              </Tab>
              <Tab>
                <Icon as={FaPalette} mr={2} />
                Theme Builder
              </Tab>
              <Tab>
                <Icon as={FiTool} mr={2} />
                Builders
              </Tab>
              <Tab>
                <Icon as={FiZap} mr={2} />
                Automation
              </Tab>
            </TabList>

            <TabPanels>
              {/* General Settings Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  {/* Basic Settings */}
                  <Card>
                    <CardHeader>
                      <Heading size="md">Basic Settings</Heading>
                    </CardHeader>
                    <CardBody>
                      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                        <VStack spacing={4} align="stretch">
                          <FormControl>
                            <FormLabel>Bot Name</FormLabel>
                            <Input
                              value={guildData.botName}
                              onChange={(e) => handleSettingChange('botName', e.target.value)}
                              placeholder="Enter bot name"
                            />
                          </FormControl>
                          <FormControl>
                            <FormLabel>Command Prefix</FormLabel>
                            <Input
                              value={guildData.prefix}
                              onChange={(e) => handleSettingChange('prefix', e.target.value)}
                              placeholder="Enter command prefix"
                            />
                          </FormControl>
                        </VStack>
                        <VStack spacing={4} align="stretch">
                          <FormControl>
                            <FormLabel>Server Name</FormLabel>
                            <Input
                              value={guildData.guildName || ''}
                              isReadOnly
                              bg="gray.50"
                              _dark={{ bg: 'gray.700' }}
                            />
                          </FormControl>
                          <FormControl>
                            <FormLabel>Server ID</FormLabel>
                            <Input
                              value={guildData.guildId || ''}
                              isReadOnly
                              bg="gray.50"
                              _dark={{ bg: 'gray.700' }}
                              fontFamily="mono"
                              fontSize="sm"
                            />
                          </FormControl>
                        </VStack>
                      </SimpleGrid>
                    </CardBody>
                  </Card>

                  {/* Roles Section */}
                  <Card>
                    <CardHeader>
                      <VStack spacing={4} align="stretch">
                        <HStack justify="space-between">
                          <Heading size="md">
                            <Icon as={FiUsers} mr={2} />
                            Roles ({roles.length})
                          </Heading>
                          <HStack spacing={2}>
                            <Button
                              leftIcon={<FiPlus />}
                              colorScheme="green"
                              onClick={handleRoleCreate}
                              isDisabled={isRateLimited}
                              size="sm"
                            >
                              Create Role
                            </Button>
                          </HStack>
                        </HStack>

                        {selectedRoles.length > 0 && (
                          <HStack justify="space-between" p={3} bg="blue.50" _dark={{ bg: 'blue.900' }} borderRadius="md">
                            <Text fontSize="sm" fontWeight="medium">
                              {selectedRoles.length} role(s) selected
                            </Text>
                            <HStack spacing={2}>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setSelectedRoles([])}
                              >
                                Clear Selection
                              </Button>
                              <Button
                                size="sm"
                                colorScheme="red"
                                leftIcon={<FiTrash2 />}
                                onClick={handleBulkDeleteRoles}
                                isLoading={bulkDeleting}
                                isDisabled={isBulkRateLimited}
                              >
                                Delete Selected
                              </Button>
                            </HStack>
                          </HStack>
                        )}
                      </VStack>
                    </CardHeader>
                    <CardBody>
                      {loading ? (
                        <VStack spacing={3}>
                          {[...Array(3)].map((_, i) => (
                            <Skeleton key={i} height="60px" />
                          ))}
                        </VStack>
                      ) : (
                        <Box overflowX="auto">
                          <Table variant="simple" size="sm">
                            <Thead>
                              <Tr>
                                <Th>
                                  <Checkbox
                                    isChecked={selectedRoles.length === roles.filter(r => r.name !== '@everyone').length && roles.length > 1}
                                    isIndeterminate={selectedRoles.length > 0 && selectedRoles.length < roles.filter(r => r.name !== '@everyone').length}
                                    onChange={selectAllRoles}
                                  />
                                </Th>
                                <Th>Role</Th>
                                <Th>Members</Th>
                                <Th>Permissions</Th>
                                <Th>Actions</Th>
                              </Tr>
                            </Thead>
                            <Tbody>
                              {(roles || []).map((role) => (
                                <Tr key={role.id}>
                                  <Td>
                                    <Checkbox
                                      isChecked={selectedRoles.includes(role.id)}
                                      onChange={() => toggleRoleSelection(role.id)}
                                      isDisabled={role.name === '@everyone'}
                                    />
                                  </Td>
                                  <Td>
                                    <HStack>
                                      <Box
                                        w={4}
                                        h={4}
                                        rounded="full"
                                        bg={role.color ? `#${role.color.toString(16).padStart(6, '0')}` : 'gray.500'}
                                      />
                                      <Text>{role.name}</Text>
                                    </HStack>
                                  </Td>
                                  <Td>
                                    <Badge colorScheme="blue">0</Badge>
                                  </Td>
                                  <Td>
                                    <HStack wrap="wrap" spacing={1}>
                                      {(decodePermissions(role.permissions) || []).slice(0, 3).map((perm) => (
                                        <Badge
                                          key={perm}
                                          colorScheme={PERMISSION_BADGES[perm]?.color || 'gray'}
                                          size="sm"
                                        >
                                          {PERMISSION_BADGES[perm]?.label || perm}
                                        </Badge>
                                      ))}
                                      {decodePermissions(role.permissions).length > 3 && (
                                        <Badge colorScheme="gray" size="sm">
                                          +{decodePermissions(role.permissions).length - 3}
                                        </Badge>
                                      )}
                                    </HStack>
                                  </Td>
                                  <Td>
                                    <HStack spacing={2}>
                                      <Tooltip label="Edit Role">
                                        <IconButton
                                          aria-label="Edit role"
                                          icon={<FiEdit2 />}
                                          size="sm"
                                          variant="ghost"
                                          colorScheme="blue"
                                          onClick={() => handleRoleEdit(role)}
                                          isDisabled={isRateLimited}
                                        />
                                      </Tooltip>
                                      <Tooltip label="Delete Role">
                                        <IconButton
                                          aria-label="Delete role"
                                          icon={<FiTrash2 />}
                                          size="sm"
                                          variant="ghost"
                                          colorScheme="red"
                                          isDisabled={isRateLimited}
                                        />
                                      </Tooltip>
                                    </HStack>
                                  </Td>
                                </Tr>
                              ))}
                            </Tbody>
                          </Table>
                        </Box>
                      )}
                    </CardBody>
                  </Card>

                  {/* Channels Section */}
                  <Card>
                    <CardHeader>
                      <VStack spacing={4} align="stretch">
                        <HStack justify="space-between">
                          <Heading size="md">
                            <Icon as={FiHash} mr={2} />
                            Channels ({channels.length})
                          </Heading>
                          <HStack spacing={2}>
                            <Button
                              leftIcon={<FiPlus />}
                              colorScheme="blue"
                              onClick={onCreateChannelOpen}
                              size="sm"
                            >
                              Create Channel
                            </Button>
                          </HStack>
                        </HStack>

                        {selectedChannels.length > 0 && (
                          <HStack justify="space-between" p={3} bg="blue.50" _dark={{ bg: 'blue.900' }} borderRadius="md">
                            <Text fontSize="sm" fontWeight="medium">
                              {selectedChannels.length} channel(s) selected
                            </Text>
                            <HStack spacing={2}>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setSelectedChannels([])}
                              >
                                Clear Selection
                              </Button>
                              <Button
                                size="sm"
                                colorScheme="red"
                                leftIcon={<FiTrash2 />}
                                onClick={handleBulkDeleteChannels}
                                isLoading={bulkDeleting}
                                isDisabled={isBulkRateLimited}
                              >
                                Delete Selected
                              </Button>
                            </HStack>
                          </HStack>
                        )}
                      </VStack>
                    </CardHeader>
                    <CardBody>
                      {channelsLoading ? (
                        <VStack spacing={3}>
                          {[...Array(5)].map((_, i) => (
                            <Skeleton key={i} height="50px" />
                          ))}
                        </VStack>
                      ) : channels.length === 0 ? (
                        <Text color="gray.500" textAlign="center" py={8}>
                          No channels found
                        </Text>
                      ) : (
                        <Box overflowX="auto">
                          <Table variant="simple" size="sm">
                            <Thead>
                              <Tr>
                                <Th>
                                  <Checkbox
                                    isChecked={selectedChannels.length === channels.length && channels.length > 0}
                                    isIndeterminate={selectedChannels.length > 0 && selectedChannels.length < channels.length}
                                    onChange={selectAllChannels}
                                  />
                                </Th>
                                <Th>Name</Th>
                                <Th>Type</Th>
                                <Th>Category</Th>
                                <Th>Position</Th>
                                <Th>Actions</Th>
                              </Tr>
                            </Thead>
                            <Tbody>
                              {(channels || []).map((channel) => {
                                const typeConfig = CHANNEL_TYPE_CONFIG[channel.type] || { icon: FiMessageSquare, color: 'gray', label: 'Other' };
                                return (
                                  <Tr key={channel.id}>
                                    <Td>
                                      <Checkbox
                                        isChecked={selectedChannels.includes(channel.id)}
                                        onChange={() => toggleChannelSelection(channel.id)}
                                      />
                                    </Td>
                                    <Td>
                                      <HStack>
                                        <Icon
                                          as={typeConfig.icon}
                                          color={`${typeConfig.color}.400`}
                                        />
                                        <Text>{channel.name}</Text>
                                      </HStack>
                                    </Td>
                                    <Td>
                                      <Badge colorScheme={typeConfig.color}>{typeConfig.label}</Badge>
                                    </Td>
                                    <Td>
                                      <Text color="gray.500">{getParentName(channel.parent_id)}</Text>
                                    </Td>
                                    <Td>
                                      <Text color="gray.500">{channel.position}</Text>
                                    </Td>
                                    <Td>
                                      <HStack spacing={2}>
                                        <Tooltip label="Edit Channel">
                                          <IconButton
                                            aria-label="Edit channel"
                                            icon={<FiEdit2 />}
                                            size="sm"
                                            variant="ghost"
                                            colorScheme="blue"
                                            onClick={() => handleChannelEdit(channel)}
                                            isDisabled={isRateLimited}
                                          />
                                        </Tooltip>
                                        <Tooltip label="Delete Channel">
                                          <IconButton
                                            aria-label="Delete channel"
                                            icon={<FiTrash2 />}
                                            size="sm"
                                            variant="ghost"
                                            colorScheme="red"
                                            onClick={() => handleChannelDelete(channel.id)}
                                            isDisabled={isRateLimited}
                                          />
                                        </Tooltip>
                                      </HStack>
                                    </Td>
                                  </Tr>
                                );
                              })}
                            </Tbody>
                          </Table>
                        </Box>
                      )}
                    </CardBody>
                  </Card>
                </VStack>
              </TabPanel>

              {/* Theme Builder Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Box>
                    <Heading size="md" mb={4}>🎨 Theme Builder</Heading>
                    <Text color="gray.600" _dark={{ color: 'gray.300' }} mb={6}>
                      Create and customize your own themes with the advanced color builder
                    </Text>
                  </Box>

                  <Card>
                    <CardHeader>
                      <HStack justify="space-between" align="center">
                        <Heading size="sm">Theme Builder & Presets</Heading>
                        <Button
                          leftIcon={<FaPalette />}
                          colorScheme="purple"
                          onClick={onColorBuilderOpen}
                          size="sm"
                        >
                          Create Custom Theme
                        </Button>
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={4} align="stretch">
                        <Text fontSize="sm" color="gray.500" mb={2}>
                          Choose from pre-built themes or create your own custom theme
                        </Text>

                        {/* Built-in Theme Presets */}
                        <VStack spacing={3} align="stretch">
                          <Text fontSize="xs" fontWeight="bold" color="gray.400" textTransform="uppercase">
                            Built-in Themes
                          </Text>
                          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={3}>
                            {COLOR_SCHEMES.map((scheme) => (
                              <Box
                                key={scheme.id}
                                p={3}
                                border="1px"
                                borderColor={currentScheme.id === scheme.id ? scheme.colors.primary : 'gray.200'}
                                borderRadius="md"
                                cursor="pointer"
                                transition="all 0.2s"
                                bg={currentScheme.id === scheme.id ? `${scheme.colors.primary}10` : 'transparent'}
                                _hover={{
                                  borderColor: scheme.colors.primary,
                                  transform: 'translateY(-1px)',
                                }}
                                onClick={() => setColorScheme(scheme.id)}
                              >
                                <VStack spacing={2} align="stretch">
                                  <HStack justify="space-between">
                                    <Text fontSize="sm" fontWeight="medium">
                                      {scheme.name}
                                    </Text>
                                    {currentScheme.id === scheme.id && (
                                      <Badge colorScheme="green" size="sm">Active</Badge>
                                    )}
                                  </HStack>
                                  <Text fontSize="xs" color="gray.500" lineHeight="1.3">
                                    {scheme.description}
                                  </Text>
                                  <HStack spacing={1}>
                                    <Box w={3} h={3} bg={scheme.colors.primary} borderRadius="full" />
                                    <Box w={3} h={3} bg={scheme.colors.secondary} borderRadius="full" />
                                    <Box w={3} h={3} bg={scheme.colors.accent} borderRadius="full" />
                                    <Box w={3} h={3} bg={scheme.colors.success} borderRadius="full" />
                                  </HStack>
                                </VStack>
                              </Box>
                            ))}
                          </SimpleGrid>
                        </VStack>

                        {/* Custom Themes */}
                        {customSchemes.length > 0 && (
                          <VStack spacing={3} align="stretch">
                            <Text fontSize="xs" fontWeight="bold" color="gray.400" textTransform="uppercase">
                              Custom Themes
                            </Text>
                            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={3}>
                              {customSchemes.map((scheme) => (
                                <Box
                                  key={scheme.id}
                                  p={3}
                                  border="1px"
                                  borderColor={currentScheme.id === scheme.id ? scheme.colors.primary : 'gray.200'}
                                  borderRadius="md"
                                  position="relative"
                                  transition="all 0.2s"
                                  bg={currentScheme.id === scheme.id ? `${scheme.colors.primary}10` : 'transparent'}
                                  _hover={{
                                    borderColor: scheme.colors.primary,
                                    transform: 'translateY(-1px)',
                                  }}
                                >
                                  <VStack spacing={2} align="stretch">
                                    <HStack justify="space-between">
                                      <Text
                                        fontSize="sm"
                                        fontWeight="medium"
                                        cursor="pointer"
                                        onClick={() => setColorScheme(scheme.id)}
                                        flex="1"
                                      >
                                        {scheme.name}
                                      </Text>
                                      <HStack spacing={1}>
                                        {currentScheme.id === scheme.id && (
                                          <Badge colorScheme="green" size="sm">Active</Badge>
                                        )}
                                        <Badge colorScheme="purple" size="sm">Custom</Badge>
                                        <Tooltip label="Delete Custom Theme">
                                          <IconButton
                                            aria-label="Delete theme"
                                            icon={<FiTrash2 />}
                                            size="xs"
                                            variant="ghost"
                                            colorScheme="red"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              deleteCustomScheme(scheme.id);
                                              toast({
                                                title: 'Theme Deleted',
                                                description: `${scheme.name} has been deleted`,
                                                status: 'success',
                                                duration: 3000,
                                              });
                                            }}
                                          />
                                        </Tooltip>
                                      </HStack>
                                    </HStack>
                                    <Text
                                      fontSize="xs"
                                      color="gray.500"
                                      lineHeight="1.3"
                                      cursor="pointer"
                                      onClick={() => setColorScheme(scheme.id)}
                                    >
                                      {scheme.description}
                                    </Text>
                                    <HStack
                                      spacing={1}
                                      cursor="pointer"
                                      onClick={() => setColorScheme(scheme.id)}
                                    >
                                      <Box w={3} h={3} bg={scheme.colors.primary} borderRadius="full" />
                                      <Box w={3} h={3} bg={scheme.colors.secondary} borderRadius="full" />
                                      <Box w={3} h={3} bg={scheme.colors.accent} borderRadius="full" />
                                      <Box w={3} h={3} bg={scheme.colors.success} borderRadius="full" />
                                    </HStack>
                                  </VStack>
                                </Box>
                              ))}
                            </SimpleGrid>
                          </VStack>
                        )}
                      </VStack>
                    </CardBody>
                  </Card>
                </VStack>
              </TabPanel>

              {/* Builders Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Box>
                    <Heading size="md" mb={4}>🛠️ Builders & Tools</Heading>
                    <Text color="gray.600" _dark={{ color: 'gray.300' }} mb={6}>
                      Create custom content and manage server features with powerful builders
                    </Text>
                  </Box>

                  <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                    <Card>
                      <CardHeader>
                        <Heading size="sm">Content Builders</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={4} align="stretch">
                          <Button
                            leftIcon={<FiZap />}
                            colorScheme="green"
                            onClick={() => window.open('/admin/experimental/addon-builder', '_blank')}
                          >
                            Addon Builder
                          </Button>
                          <Button
                            leftIcon={<FiMessageSquare />}
                            colorScheme="blue"
                            onClick={() => window.open('/admin/applications-builder', '_blank')}
                          >
                            Applications Builder
                          </Button>
                          <Button
                            leftIcon={<FiMessageSquare />}
                            colorScheme="purple"
                            onClick={() => window.open('/admin/embed-builder', '_blank')}
                            isDisabled
                          >
                            Message Builder (Coming Soon)
                          </Button>
                        </VStack>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Management Tools</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={4} align="stretch">
                          <Button
                            leftIcon={<FiSettings />}
                            colorScheme="orange"
                            onClick={() => window.open('/admin/addons', '_blank')}
                          >
                            Manage Addons
                          </Button>
                          <Button
                            leftIcon={<FiSettings />}
                            colorScheme="teal"
                            onClick={() => window.open('/admin/commands', '_blank')}
                          >
                            Command Manager
                          </Button>
                          <Button
                            leftIcon={<FiSettings />}
                            colorScheme="cyan"
                            onClick={() => window.open('/admin/applications', '_blank')}
                          >
                            Application Manager
                          </Button>
                        </VStack>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </VStack>
              </TabPanel>

              {/* Automation Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Box>
                    <Heading size="md" mb={4}>⚡ Automation & Activities</Heading>
                    <Text color="gray.600" _dark={{ color: 'gray.300' }} mb={6}>
                      Set up automated features and server activities
                    </Text>
                  </Box>

                  <Card>
                    <CardHeader>
                      <HStack justify="space-between">
                        <Heading size="md">Bot Presence</Heading>
                        <Button
                          leftIcon={<FiSave />}
                          colorScheme="blue"
                          onClick={savePresence}
                          isLoading={savingPresence}
                          isDisabled={isRateLimited}
                        >
                          Save Presence
                        </Button>
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={6} align="stretch">
                        {guildData.activities.map((activity, index) => (
                          <HStack key={index} spacing={4} align="flex-end">
                            <FormControl>
                              <FormLabel fontSize="sm">Activity Type</FormLabel>
                              <Select
                                value={activity.type}
                                onChange={(e) => handleActivityChange(index, 'type', e.target.value)}
                              >
                                <option value="PLAYING">Playing</option>
                                <option value="STREAMING">Streaming</option>
                                <option value="LISTENING">Listening</option>
                                <option value="WATCHING">Watching</option>
                                <option value="COMPETING">Competing</option>
                              </Select>
                            </FormControl>
                            <FormControl>
                              <FormLabel fontSize="sm">Activity Name</FormLabel>
                              <Input
                                value={activity.name}
                                onChange={(e) => handleActivityChange(index, 'name', e.target.value)}
                                placeholder="e.g., with discord.js"
                              />
                            </FormControl>
                            <IconButton
                              aria-label="Remove activity"
                              icon={<FiTrash2 />}
                              colorScheme="red"
                              variant="ghost"
                              onClick={() => removeActivity(index)}
                            />
                          </HStack>
                        ))}
                        <Button
                          leftIcon={<FiPlus />}
                          onClick={addActivity}
                          size="sm"
                          alignSelf="flex-start"
                        >
                          Add Activity
                        </Button>
                        <Divider />
                        <FormControl>
                          <FormLabel>Activity Rotation Interval (seconds)</FormLabel>
                          <NumberInput
                            value={guildData.activityRotationInterval}
                            onChange={(valueString) => handleSettingChange('activityRotationInterval', parseInt(valueString) || 60)}
                            min={10}
                            max={3600}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                          <Text fontSize="xs" color="gray.500" mt={1}>
                            Time between rotating activities (min 10s, max 1hr).
                          </Text>
                        </FormControl>
                      </VStack>
                    </CardBody>
                  </Card>

                  <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                    <Card>
                      <CardHeader>
                        <Heading size="sm">Activity Templates</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text color="gray.500" fontSize="sm" mb={4}>
                          Pre-built activity templates to get you started quickly:
                        </Text>
                        <VStack spacing={2} align="stretch">
                          <Text fontSize="sm">• Event Management System</Text>
                          <Text fontSize="sm">• Welcome & Onboarding Flow</Text>
                          <Text fontSize="sm">• Moderation Automation</Text>
                          <Text fontSize="sm">• Custom Commands</Text>
                          <Text fontSize="sm">• Auto-Role Assignment</Text>
                          <Text fontSize="sm">• Scheduled Messages</Text>
                        </VStack>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Automation Settings</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={4} align="stretch">
                          <Text fontSize="sm" color="gray.500">
                            Configure automated server features
                          </Text>
                          <Button
                            leftIcon={<FiZap />}
                            colorScheme="red"
                            variant="outline"
                            onClick={onModerationSystemOpen}
                          >
                            Auto-Moderation
                          </Button>
                          <Button
                            leftIcon={<FiZap />}
                            colorScheme="green"
                            variant="outline"
                            onClick={onWelcomeSystemOpen}
                          >
                            Welcome System
                          </Button>
                          <Button
                            leftIcon={<FiZap />}
                            colorScheme="blue"
                            variant="outline"
                            isDisabled
                          >
                            Event Scheduler (Coming Soon)
                          </Button>
                        </VStack>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </VStack>

        {/* Modals */}
        <Suspense fallback={<Spinner />}>
          <CreateChannelDialog
            isOpen={isCreateChannelOpen}
            onClose={onCreateChannelClose}
            onSuccess={fetchChannels}
          />
        </Suspense>

        <Suspense fallback={<Spinner />}>
          <EditChannelDialog
            isOpen={isEditChannelOpen}
            onClose={onEditChannelClose}
            channel={selectedChannel}
            categories={channels.filter(c => c.type === 4)}
            onSuccess={fetchChannels}
          />
        </Suspense>

        <Suspense fallback={<Spinner />}>
          <CreateRoleDialog
            isOpen={isCreateRoleOpen}
            onClose={onCreateRoleClose}
            onSuccess={fetchGuildData}
          />
        </Suspense>

        <Suspense fallback={<Spinner />}>
          <EditRoleDialog
            isOpen={isEditRoleOpen}
            onClose={onEditRoleClose}
            role={selectedRole}
            onSuccess={fetchGuildData}
          />
        </Suspense>

        <Suspense fallback={<Spinner />}>
          <ColorBuilder
            isOpen={isColorBuilderOpen}
            onClose={onColorBuilderClose}
          />
        </Suspense>
        
        <Suspense fallback={<Spinner />}>
          <WelcomeSystemDialog
            isOpen={isWelcomeSystemOpen}
            onClose={onWelcomeSystemClose}
            channels={channels}
            roles={roles}
          />
        </Suspense>

        <Suspense fallback={<Spinner />}>
          <ModerationSystemDialog
            isOpen={isModerationSystemOpen}
            onClose={onModerationSystemClose}
            channels={channels}
            roles={roles}
          />
        </Suspense>
      </Container>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);

  if (!session) {
    return {
      redirect: {
        destination: '/api/auth/signin?callbackUrl=%2Fadmin%2Fguilds',
        permanent: false,
      },
    };
  }

  return { props: { session } };
};
