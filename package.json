{"name": "404-bot", "version": "1.0.0", "description": "A Discord bot with TypeScript, addon system, MongoDB integration and web dashboard", "type": "module", "main": "dist/index.js", "scripts": {"dev": "concurrently -k \"cross-env DASHBOARD_DEV=1 tsx src/index.ts\" \"cd src/dashboard && next dev --port 3000\"", "build": "cd src/dashboard && next build && cd .. && tsc", "start": "concurrently -k \"node --enable-source-maps dist/index.js\" \"cd src/dashboard && next start --port 3000\"", "build:dashboard": "cd src/dashboard && next build --debug", "build:optimized": "node scripts/build-optimized.js"}, "keywords": ["discord", "bot", "typescript", "mongodb", "dashboard"], "author": "OnedEyePete", "license": "MIT", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "chalk": "^5.4.1", "chokidar": "^4.0.3", "discord.js": "^14.21.0", "mongodb": "^6.17.0", "next": "^15.3.5", "next-auth": "^4.24.11", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "reactflow": "^11.11.4", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@tailwindcss/line-clamp": "^0.4.4", "@types/node": "^22.10.2", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitest/coverage-v8": "^1.2.2", "autoprefixer": "^10.4.19", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "eslint": "^9.29.0", "postcss": "^8.4.38", "prettier": "^3.2.5", "rimraf": "^6.0.1", "tailwindcss": "^3.4.3", "tsx": "^4.19.2", "typescript": "^5.7.2", "vitest": "^1.2.2"}, "engines": {"node": ">=18.0.0"}}