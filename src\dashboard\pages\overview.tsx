
import { FaQuoteLeft, FaQuoteRight, FaRobot } from 'react-icons/fa';
import { FiUsers, FiMessageSquare, FiActivity, FiServer, FiTrendingUp, FiAlertCircle } from 'react-icons/fi';
import Layout from '../components/Layout';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './api/auth/[...nextauth]';
import { CARD_CONFIGS } from '../config/cards';
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { OverviewCard } from '../components/OverviewCard';
import NextLink from 'next/link';
import React from 'react';
import { useTheme } from '../contexts/ThemeContext';

export default function Overview() {
  const { data: session } = useSession();
  const { currentScheme } = useTheme();
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const [serverRes, botRes] = await Promise.all([
          fetch('/api/analytics/server'),
          fetch('/api/analytics/bot')
        ]);

        if (!serverRes.ok || !botRes.ok) {
          throw new Error('Failed to fetch analytics');
        }

        const [serverData, botData] = await Promise.all([
          serverRes.json(),
          botRes.json()
        ]);

        setAnalyticsData({
          serverStats: serverData.serverStats,
          botStats: botData.botStats,
        });
      } catch (error) {
        console.error('Error fetching analytics:', error);
        setError('Failed to load analytics data');
        // Fallback to mock data
        setAnalyticsData({
          serverStats: {
            totalMembers: 0,
            onlineMembers: 0,
            totalChannels: 0,
            totalRoles: 0,
          },
          botStats: {
            commandsToday: 0,
            uptime: 'Unknown',
            responseTime: '0ms',
            activeAddons: 0,
            inactiveAddons: 0,
          }
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, []);

  const quotes = [
    '"Talk is cheap. Show me the code." – Linus Torvalds',
    '"Programs must be written for people to read, and only incidentally for machines to execute." – Harold Abelson',
    '"Any fool can write code that a computer can understand. Good programmers write code that humans can understand." – Martin Fowler',
    '"First, solve the problem. Then, write the code." – John Johnson',
    '"404 Chill Not Found? Keep calm and debug on." – Unknown',
    "It's not a bug – it's an undocumented feature.",
    '"The best error message is the one that never shows up." – Thomas Fuchs',
    "Code is like humor. When you have to explain it, it's bad.",
    '"Experience is the name everyone gives to their mistakes." – Oscar Wilde',
    '"In order to be irreplaceable, one must always be different." – Coco Chanel',
  ];

  // Use a stable quote selection based on the day of the month
  const getQuoteOfTheDay = () => {
    const today = new Date();
    const dayOfMonth = today.getDate(); // 1-31
    return quotes[dayOfMonth % quotes.length];
  };

  const quoteOfTheDay = getQuoteOfTheDay();

  // Filter cards based on user role (excluding specific overview cards)
  const filteredCards = CARD_CONFIGS.filter(card => {
    if (card.requiredRole === 'admin') {
      return (session?.user as any)?.isAdmin;
    }
    if (card.requiredRole === 'moderator') {
      return (session?.user as any)?.isModerator;
    }
    // Exclude 'overview', 'experimental', 'gameservers', 'applications', and 'tickets' cards
    return !['overview', 'experimental', 'gameservers', 'applications', 'tickets'].includes(card.id);
  });

  // Dynamic styling functions
  const getAnalyticsSectionStyle = () => {
    return `
      relative p-8
      before:absolute before:inset-0 
      before:bg-[radial-gradient(circle_at_center,rgba(66,153,225,0.1)_0%,transparent_70%)]
      before:-z-10
    `;
  };

  const getStatCardStyle = (isError = false) => {
    const baseStyle = `
      bg-white/5 backdrop-blur-xl rounded-xl 
      border border-white/10 p-6 
      transition-all duration-300
      hover:translate-y-[-4px] hover:shadow-lg
    `;

    return isError 
      ? `${baseStyle} border-red-400 hover:border-red-500` 
      : baseStyle;
  };

  const getStatNumberStyle = (color: string) => {
    return `
      text-2xl font-bold 
      bg-gradient-to-r from-[${color}] to-[${color}] 
      bg-clip-text text-transparent
    `;
  };

  return (
    <Layout>
      <div className={getAnalyticsSectionStyle()}>
        {/* Analytics Header */}
        <div className="text-center mb-8">
          <h1 
            className="text-2xl font-bold flex items-center justify-center"
            style={{ color: currentScheme.colors.text }}
          >
            <span className="mr-2">📊</span>
            Server Analytics
          </h1>
        </div>
        
        {/* Analytics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: 4 }).map((_, i) => (
              <div 
                key={i} 
                className="bg-white/10 animate-pulse h-48 rounded-xl"
              />
            ))
          ) : (
            <>
              {/* Total Members Card */}
              <div className={getStatCardStyle()}>
                <div className="flex items-center space-x-3 mb-4">
                  <FiUsers className="w-6 h-6 text-blue-400" />
                  <span className="text-gray-300">Total Members</span>
                </div>
                <h2 className={getStatNumberStyle(currentScheme.colors.primary)}>
                  {analyticsData?.serverStats.totalMembers.toLocaleString() || '0'}
                </h2>
                <div className="mt-2 space-y-1 text-sm">
                  <p className="text-green-400 flex items-center">
                    <FiTrendingUp className="mr-1" />
                    {analyticsData?.serverStats.onlineMembers || '0'} online
                  </p>
                  <p className="text-green-300">
                    +{analyticsData?.serverStats.newMembersToday || 0} joined
                  </p>
                  <p className="text-red-400">
                    -{analyticsData?.serverStats.leftMembersToday || 0} left
                  </p>
                </div>
              </div>

              {/* Channels Card */}
              <div className={getStatCardStyle()}>
                <div className="flex items-center space-x-3 mb-4">
                  <FiMessageSquare className="w-6 h-6 text-green-400" />
                  <span className="text-gray-300">Channels</span>
                </div>
                <h2 className={getStatNumberStyle(currentScheme.colors.accent)}>
                  {analyticsData?.serverStats.totalChannels || '0'}
                </h2>
                <p className="mt-2 text-sm text-gray-400">
                  {analyticsData?.serverStats.totalRoles || '0'} roles
                </p>
              </div>

              {/* Commands Card */}
              <div className={getStatCardStyle()}>
                <div className="flex items-center space-x-3 mb-4">
                  <FiActivity className="w-6 h-6 text-purple-400" />
                  <span className="text-gray-300">Commands Today</span>
                </div>
                <h2 className={getStatNumberStyle(currentScheme.colors.primary)}>
                  {analyticsData?.botStats.commandsToday || '0'}
                </h2>
                <p className="mt-2 text-sm text-gray-400">
                  {analyticsData?.botStats.responseTime || '0ms'} avg response
                </p>
              </div>

              {/* Bot Uptime Card */}
              <div className={getStatCardStyle()}>
                <div className="flex items-center space-x-3 mb-4">
                  <FiServer className="w-6 h-6 text-orange-400" />
                  <span className="text-gray-300">Bot Uptime</span>
                </div>
                <h2 className={getStatNumberStyle(currentScheme.colors.accent)}>
                  {analyticsData?.botStats.uptime || 'Unknown'}
                </h2>
                <div className="mt-2 space-y-1 text-sm">
                  <p className="text-green-400">
                    {analyticsData?.botStats.activeAddons || '0'} addons active
                  </p>
                  <p className="text-red-400">
                    {analyticsData?.botStats.inactiveAddons || '0'} addons inactive
                  </p>
                </div>
              </div>

              {/* Errors Card (if applicable) */}
              {analyticsData?.botStats.errorsToday > 0 && (
                <NextLink href="/admin/errors" className="contents">
                  <div className={getStatCardStyle(true)}>
                    <div className="flex items-center space-x-3 mb-4">
                      <FiAlertCircle className="w-6 h-6 text-red-400" />
                      <span className="text-gray-300">Errors Today</span>
                    </div>
                    <h2 className="text-2xl font-bold text-red-400">
                      {analyticsData.botStats.errorsToday}
                    </h2>
                    <p className="mt-2 text-sm text-red-300">
                      Needs attention
                    </p>
                  </div>
                </NextLink>
              )}
            </>
          )}
        </div>

        {/* Quote Section */}
        <div className="mt-12 text-center">
          <div className="max-w-2xl mx-auto relative">
            <FaQuoteLeft className="absolute -left-8 top-0 text-gray-600 opacity-50 text-3xl" />
            <p 
              className="text-lg italic text-gray-300 px-8"
              style={{ color: currentScheme.colors.textSecondary }}
            >
              {quoteOfTheDay}
            </p>
            <FaQuoteRight className="absolute -right-8 bottom-0 text-gray-600 opacity-50 text-3xl" />
          </div>
        </div>
      </div>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);
  if (!session) {
    return {
      redirect: {
        destination: '/signin',
        permanent: false,
      },
    };
  }
  return { props: {} };
};
