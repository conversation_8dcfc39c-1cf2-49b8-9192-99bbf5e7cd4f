import React, { useState, memo, useEffect } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Select,
  Input,
  Textarea,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  useDisclosure,
  IconButton,
  Badge,
  Alert,
  AlertIcon,
  AlertDescription,
  SimpleGrid,
  Switch,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Flex,
  Spacer,
  Tooltip,
  Code,
  Collapse,
  Divider,
  useColorModeValue,
} from '@chakra-ui/react';
import { useTheme } from '../../contexts/ThemeContext';

// Custom type definitions to replace reactflow types
interface NodeProps<T = any> {
  data: T;
  selected?: boolean;
  id?: string;
  updateNodeData?: (nodeId: string, newData: any) => void;
}

interface HandleProps {
  type: 'source' | 'target';
  position: 'top' | 'bottom' | 'left' | 'right';
  style?: React.CSSProperties;
}

const Handle: React.FC<HandleProps> = ({ type, position, style }) => (
  <div 
    style={{
      position: 'absolute',
      ...style,
      ...(position === 'top' ? { top: '-6px', left: '50%', transform: 'translateX(-50%)' } : {})
    }}
  />
);

const Position = {
  Top: 'top' as const,
  Bottom: 'bottom' as const,
  Left: 'left' as const,
  Right: 'right' as const
};

interface EmbedField {
  name: string;
  value: string;
  inline?: boolean;
}

interface EmbedAuthor {
  name?: string;
  url?: string;
  iconUrl?: string;
}

interface EmbedFooter {
  text?: string;
  iconUrl?: string;
}

interface EmbedConfig {
  title?: string;
  description?: string;
  color?: string;
  url?: string;
  timestamp?: boolean;
  thumbnail?: string;
  image?: string;
  author?: EmbedAuthor;
  footer?: EmbedFooter;
  fields?: EmbedField[];
}

interface ActionNodeData {
  label: string;
  actionType?: string;
  message?: string;
  channel?: string;
  role?: string;
  user?: string;
  embed?: EmbedConfig;
  reason?: string;
  duration?: number;
  deleteMessages?: boolean;
  reaction?: string;
  channelName?: string;
  channelType?: string;
  permissions?: string[];
}

const actionTypes = [
  { value: 'sendMessage', label: '💬 Send Message', category: 'Message' },
  { value: 'sendEmbed', label: '📋 Send Embed', category: 'Message' },
  { value: 'editMessage', label: '✏️ Edit Message', category: 'Message' },
  { value: 'deleteMessage', label: '🗑️ Delete Message', category: 'Message' },
  { value: 'addReaction', label: '👍 Add Reaction', category: 'Message' },
  { value: 'removeReaction', label: '👎 Remove Reaction', category: 'Message' },
  { value: 'addRole', label: '🎭 Add Role', category: 'Roles' },
  { value: 'removeRole', label: '🎭 Remove Role', category: 'Roles' },
  { value: 'kickUser', label: '👢 Kick User', category: 'Moderation' },
  { value: 'banUser', label: '🔨 Ban User', category: 'Moderation' },
  { value: 'timeoutUser', label: '⏰ Timeout User', category: 'Moderation' },
  { value: 'unbanUser', label: '🔓 Unban User', category: 'Moderation' },
  { value: 'createChannel', label: '📺 Create Channel', category: 'Channel' },
  { value: 'deleteChannel', label: '🗑️ Delete Channel', category: 'Channel' },
  { value: 'lockChannel', label: '🔒 Lock Channel', category: 'Channel' },
  { value: 'unlockChannel', label: '🔓 Unlock Channel', category: 'Channel' },
  { value: 'sendDM', label: '📬 Send DM', category: 'Message' },
  { value: 'createThread', label: '🧵 Create Thread', category: 'Channel' },
  { value: 'pinMessage', label: '📌 Pin Message', category: 'Message' },
  { value: 'unpinMessage', label: '📌 Unpin Message', category: 'Message' },
];

// Available variables organized by category
const availableVariables = {
  user: [
    { name: '{user.id}', description: 'User ID', icon: '🆔' },
    { name: '{user.username}', description: 'Username', icon: '👤' },
    { name: '{user.displayName}', description: 'Display Name', icon: '📝' },
    { name: '{user.tag}', description: 'User Tag (username#0000)', icon: '🏷️' },
    { name: '{user.mention}', description: 'User Mention (<@id>)', icon: '📢' },
    { name: '{user.avatar}', description: 'Avatar URL', icon: '🖼️' },
    { name: '{user.createdAt}', description: 'Account Creation Date', icon: '📅' },
    { name: '{user.joinedAt}', description: 'Server Join Date', icon: '🚪' },
    { name: '{user.roles}', description: 'User Roles', icon: '🎭' },
    { name: '{user.permissions}', description: 'User Permissions', icon: '🔐' },
  ],
  channel: [
    { name: '{channel.id}', description: 'Channel ID', icon: '🆔' },
    { name: '{channel.name}', description: 'Channel Name', icon: '📺' },
    { name: '{channel.mention}', description: 'Channel Mention (<#id>)', icon: '📢' },
    { name: '{channel.type}', description: 'Channel Type', icon: '📋' },
    { name: '{channel.topic}', description: 'Channel Topic', icon: '💬' },
    { name: '{channel.memberCount}', description: 'Member Count', icon: '👥' },
    { name: '{channel.createdAt}', description: 'Channel Creation Date', icon: '📅' },
  ],
  server: [
    { name: '{server.id}', description: 'Server ID', icon: '🆔' },
    { name: '{server.name}', description: 'Server Name', icon: '🏠' },
    { name: '{server.icon}', description: 'Server Icon URL', icon: '🖼️' },
    { name: '{server.memberCount}', description: 'Member Count', icon: '👥' },
    { name: '{server.createdAt}', description: 'Server Creation Date', icon: '📅' },
    { name: '{server.owner}', description: 'Server Owner', icon: '👑' },
    { name: '{server.boostLevel}', description: 'Server Boost Level', icon: '🚀' },
    { name: '{server.boostCount}', description: 'Server Boost Count', icon: '💎' },
  ],
  message: [
    { name: '{message.id}', description: 'Message ID', icon: '🆔' },
    { name: '{message.content}', description: 'Message Content', icon: '💬' },
    { name: '{message.author}', description: 'Message Author', icon: '👤' },
    { name: '{message.channel}', description: 'Message Channel', icon: '📺' },
    { name: '{message.createdAt}', description: 'Message Creation Date', icon: '📅' },
    { name: '{message.editedAt}', description: 'Message Edit Date', icon: '✏️' },
    { name: '{message.reactions}', description: 'Message Reactions', icon: '👍' },
    { name: '{message.attachments}', description: 'Message Attachments', icon: '📎' },
  ],
  api: [
    { name: '{response.data}', description: 'API Response Data', icon: '📊' },
    { name: '{response.status}', description: 'HTTP Status Code', icon: '🔢' },
    { name: '{response.headers}', description: 'Response Headers', icon: '📋' },
    { name: '{response.message}', description: 'Response Message', icon: '💬' },
    { name: '{response.error}', description: 'Error Message', icon: '❌' },
  ],
  random: [
    { name: '{random.number}', description: 'Random Number (1-100)', icon: '🎲' },
    { name: '{random.uuid}', description: 'Random UUID', icon: '🆔' },
    { name: '{random.choice}', description: 'Random Choice from Array', icon: '🎯' },
    { name: '{random.color}', description: 'Random Hex Color', icon: '🎨' },
  ],
  date: [
    { name: '{date.now}', description: 'Current Date/Time', icon: '⏰' },
    { name: '{date.today}', description: 'Today\'s Date', icon: '📅' },
    { name: '{date.timestamp}', description: 'Unix Timestamp', icon: '🕐' },
    { name: '{date.iso}', description: 'ISO Date String', icon: '📝' },
  ],
};

const ActionNode = memo(({ data, selected, id, updateNodeData: updateParentNodeData }: NodeProps<ActionNodeData> & { updateNodeData?: (nodeId: string, newData: any) => void }) => {
  const { currentScheme } = useTheme();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [nodeData, setNodeData] = useState<ActionNodeData>(() => ({
    embed: {
      fields: [],
      author: { name: '' },
      footer: { text: '' },
    },
    ...data
  }));
  const [showVariables, setShowVariables] = useState(false);
  const [embedPreview, setEmbedPreview] = useState(false);
  const [guildData, setGuildData] = useState<{
    channels: Array<{ id: string; name: string; type: string }>;
    roles: Array<{ id: string; name: string; color: string }>;
    members: Array<{ id: string; username: string; displayName: string }>;
  } | null>(null);
  const [loadingGuildData, setLoadingGuildData] = useState(false);

  const updateNodeData = (updates: Partial<ActionNodeData>) => {
    setNodeData(prev => ({ ...prev, ...updates }));
  };

  const handleModalClose = () => {
    // Update parent nodes array when modal closes
    if (updateParentNodeData && id) {
      updateParentNodeData(id, nodeData);
    }
    onClose();
  };

  const getActionLabel = (actionType: string) => {
    return actionTypes.find(a => a.value === actionType)?.label || actionType;
  };

  const getActionIcon = (actionType: string) => {
    const action = actionTypes.find(a => a.value === actionType);
    return action?.label.split(' ')[0] || '🎯';
  };

  const copyVariable = (variable: string) => {
    navigator.clipboard.writeText(variable);
  };

  const fetchGuildData = async () => {
    if (guildData || loadingGuildData) return; // Don't fetch if already loaded or loading
    
    setLoadingGuildData(true);
    try {
      const response = await fetch('/api/admin/experimental/addon-builder/guild-data');
      if (response.ok) {
        const data = await response.json();
        setGuildData({
          channels: data.channels,
          roles: data.roles,
          members: data.members,
        });
      } else {
        console.error('Failed to fetch guild data:', await response.text());
      }
    } catch (error) {
      console.error('Error fetching guild data:', error);
    } finally {
      setLoadingGuildData(false);
    }
  };

  // Fetch guild data when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchGuildData();
    }
  }, [isOpen]);

  const addEmbedField = () => {
    const currentFields = nodeData.embed?.fields || [];
    updateNodeData({
      embed: {
        ...nodeData.embed,
        fields: [...currentFields, { name: '', value: '', inline: false }]
      }
    });
  };

  const updateEmbedField = (index: number, field: keyof EmbedField, value: any) => {
    const currentFields = nodeData.embed?.fields || [];
    const newFields = [...currentFields];
    newFields[index] = { ...newFields[index], [field]: value };
    updateNodeData({
      embed: {
        ...nodeData.embed,
        fields: newFields
      }
    });
  };

  const removeEmbedField = (index: number) => {
    const currentFields = nodeData.embed?.fields || [];
    const newFields = currentFields.filter((_, i) => i !== index);
    updateNodeData({
      embed: {
        ...nodeData.embed,
        fields: newFields
      }
    });
  };

  const renderEmbedPreview = () => {
    const embed = nodeData.embed || {};
    
    return (
      <VStack spacing={3} align="stretch" maxW="500px">
        {/* Message Content Above Embed */}
        {nodeData.message && (
          <Box
            bg={currentScheme.colors.surface}
            border="1px solid"
            borderColor={currentScheme.colors.border}
            borderRadius="md"
            p={3}
          >
            <Text fontSize="sm" color={currentScheme.colors.text} fontWeight="medium">
              📩 Message Content:
            </Text>
            <Text fontSize="sm" color={currentScheme.colors.text} mt={1}>
              {nodeData.message}
            </Text>
          </Box>
        )}
        
        {/* Embed */}
        <Box
          bg={currentScheme.colors.surface}
          border="1px solid"
          borderColor={currentScheme.colors.border}
          borderRadius="md"
          p={4}
          borderLeft={`4px solid ${embed.color || '#5865F2'}`}
        >
          {embed.author?.name && (
            <HStack spacing={2} mb={2}>
              {embed.author.iconUrl && (
                <Box
                  w={6}
                  h={6}
                  borderRadius="full"
                  bg="gray.300"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  fontSize="xs"
                >
                  👤
                </Box>
              )}
              <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                {embed.author.name}
              </Text>
            </HStack>
          )}
          
          {embed.title && (
            <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text} mb={2}>
              {embed.title}
            </Text>
          )}
          
          {embed.description && (
            <Text fontSize="sm" color={currentScheme.colors.textSecondary} mb={3}>
              {embed.description}
            </Text>
          )}
          
          {embed.fields && embed.fields.length > 0 && (
            <VStack spacing={2} align="stretch" mb={3}>
              {embed.fields.map((field, index) => (
                <Box key={index}>
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                    {field.name}
                  </Text>
                  <Text fontSize="sm" color={currentScheme.colors.textSecondary}>
                    {field.value}
                  </Text>
                </Box>
              ))}
            </VStack>
          )}
          
          {embed.footer?.text && (
            <HStack spacing={2} mt={3} pt={2} borderTop="1px solid" borderColor={currentScheme.colors.border}>
              {embed.footer.iconUrl && (
                <Box
                  w={4}
                  h={4}
                  borderRadius="full"
                  bg="gray.300"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  fontSize="xs"
                >
                  ℹ️
                </Box>
              )}
              <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                {embed.footer.text}
              </Text>
            </HStack>
          )}
          
          {embed.timestamp && (
            <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={2}>
              🕒 {new Date().toLocaleString()}
            </Text>
          )}
        </Box>
      </VStack>
    );
  };

  const renderVariablesList = () => (
    <Collapse in={showVariables} animateOpacity>
      <Box
        bg={currentScheme.colors.surface}
        border="1px solid"
        borderColor={currentScheme.colors.border}
        borderRadius="md"
        p={4}
        mt={3}
        maxH="400px"
        overflowY="auto"
      >
        <Accordion allowMultiple>
          {Object.entries(availableVariables).map(([category, variables]) => (
            <AccordionItem key={category} border="none">
              <AccordionButton px={0} py={2}>
                <Box flex="1" textAlign="left">
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text} textTransform="capitalize">
                    {category} Variables
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel px={0} py={2}>
                <VStack spacing={2} align="stretch">
                  {variables.map((variable) => (
                    <HStack
                      key={variable.name}
                      spacing={2}
                      p={2}
                      bg={currentScheme.colors.background}
                      borderRadius="md"
                      cursor="pointer"
                      _hover={{ bg: currentScheme.colors.surface }}
                      onClick={() => copyVariable(variable.name)}
                    >
                      <Text fontSize="sm">{variable.icon}</Text>
                      <Code fontSize="xs" colorScheme="blue">
                        {variable.name}
                      </Code>
                      <Text fontSize="xs" color={currentScheme.colors.textSecondary} flex="1">
                        {variable.description}
                      </Text>
                      <IconButton
                        icon={<Text>📋</Text>}
                        size="xs"
                        variant="ghost"
                        aria-label="Copy variable"
                        onClick={(e) => {
                          e.stopPropagation();
                          copyVariable(variable.name);
                        }}
                      />
                    </HStack>
                  ))}
                </VStack>
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </Box>
    </Collapse>
  );

  return (
    <>
      <Box
        bg={currentScheme.colors.surface}
        border={`2px solid ${selected ? '#a855f7' : currentScheme.colors.border}`}
        borderRadius="md"
        p={2}
        minW="140px"
        maxW="180px"
        boxShadow="sm"
        position="relative"
        _hover={{
          boxShadow: 'md',
          transform: 'translateY(-1px)',
        }}
        transition="all 0.2s"
      >
        <Handle
          type="target"
          position={Position.Top}
          style={{
            background: '#a855f7',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            top: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        />
        
        <VStack spacing={1} align="stretch">
          <HStack justify="space-between" align="center">
            <HStack spacing={1}>
              <Box
                bg="purple.500"
                color="white"
                borderRadius="full"
                p={0.5}
                fontSize="xs"
              >
                🎯
              </Box>
              <Text fontSize="xs" fontWeight="bold" color={currentScheme.colors.text}>
                Action
              </Text>
            </HStack>
            <IconButton
              icon={<Text>⚙️</Text>}
              size="xs"
              variant="ghost"
              onClick={onOpen}
              aria-label="Configure action"
            />
          </HStack>
          
          <Box>
            <HStack spacing={1}>
              {nodeData.actionType && (
                <Text fontSize="xs">{getActionIcon(nodeData.actionType)}</Text>
              )}
            <Text fontSize="xs" color={currentScheme.colors.text} noOfLines={1}>
                {nodeData.actionType ? getActionLabel(nodeData.actionType).split(' ').slice(1).join(' ') : 'Select Action'}
            </Text>
            </HStack>
          </Box>
          
          {nodeData.message && (
            <Box>
              <Text fontSize="xs" color={currentScheme.colors.textSecondary} noOfLines={1}>
                {nodeData.message.length > 20 ? nodeData.message.substring(0, 20) + '...' : nodeData.message}
              </Text>
            </Box>
          )}
          
          <HStack spacing={1} flexWrap="wrap">
            {nodeData.channel && (
              <Badge size="xs" colorScheme="purple">
                #{nodeData.channel}
              </Badge>
            )}
            {nodeData.role && (
              <Badge size="xs" colorScheme="purple">
                @{nodeData.role}
              </Badge>
            )}
            {nodeData.embed?.title && (
              <Badge size="xs" colorScheme="blue">
                📋 Embed
              </Badge>
            )}
          </HStack>
        </VStack>
        
        {/* Remove source handle for Actions as they should be endpoints */}
      </Box>

      {/* Enhanced Configuration Modal */}
      <Modal isOpen={isOpen} onClose={handleModalClose} size="4xl">
        <ModalOverlay bg="blackAlpha.600" />
        <ModalContent bg={currentScheme.colors.background} border="2px solid" borderColor="purple.400" maxW="1200px">
          <ModalHeader color={currentScheme.colors.text}>
            🎯 Configure Action
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={6} align="stretch">
              {/* Variables Helper */}
              <Box>
                <HStack justify="space-between" align="center" mb={2}>
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                    Available Variables
                  </Text>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={showVariables ? <Text>👁️</Text> : <Text>👁️</Text>}
                    onClick={() => setShowVariables(!showVariables)}
                  >
                    {showVariables ? 'Hide' : 'Show'} Variables
                  </Button>
                </HStack>
                <Alert status="info" borderRadius="md" mb={2}>
                  <AlertIcon />
                  <AlertDescription fontSize="sm">
                    💡 Use variables in your actions! Click any variable below to copy it. Variables are replaced with actual values when your addon runs.
                  </AlertDescription>
                </Alert>
                {renderVariablesList()}
              </Box>

              <Divider />

              {/* Action Type Selection */}
              <FormControl>
                <FormLabel color={currentScheme.colors.text}>Action Type</FormLabel>
                <Select
                  value={nodeData.actionType || ''}
                  onChange={(e) => updateNodeData({ actionType: e.target.value })}
                  placeholder="Select an action type"
                  bg={currentScheme.colors.background}
                  color={currentScheme.colors.text}
                  borderColor={currentScheme.colors.border}
                >
                  {Object.entries(
                    actionTypes.reduce((acc, action) => {
                      if (!acc[action.category]) acc[action.category] = [];
                      acc[action.category].push(action);
                      return acc;
                    }, {} as Record<string, typeof actionTypes>)
                  ).map(([category, actions]) => (
                    <optgroup key={category} label={category}>
                      {actions.map((action) => (
                    <option key={action.value} value={action.value}>
                      {action.label}
                    </option>
                      ))}
                    </optgroup>
                  ))}
                </Select>
              </FormControl>
              
                            {/* Action-specific Configuration */}
              {nodeData.actionType && (
                <>
                  {nodeData.actionType === 'sendEmbed' ? (
                    <Tabs variant="enclosed" colorScheme="purple">
                      <TabList>
                        <Tab>Embed Builder</Tab>
                        <Tab>Preview</Tab>
                      </TabList>
                      
                      <TabPanels>
                        <TabPanel>
                          <VStack spacing={4} align="stretch">
                            {/* Channel Selection */}
                            <FormControl>
                              <FormLabel color={currentScheme.colors.text}>Channel</FormLabel>
                              <VStack spacing={2} align="stretch">
                                <Select
                                  value={nodeData.channel || ''}
                                  onChange={(e) => updateNodeData({ channel: e.target.value })}
                                  placeholder={loadingGuildData ? 'Loading channels...' : 'Select a channel'}
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                  isDisabled={loadingGuildData}
                                >
                                  {guildData?.channels
                                    .filter(channel => channel.type === 'text')
                                    .map((channel) => (
                                      <option key={channel.id} value={channel.name}>
                                        #{channel.name}
                                      </option>
                                    ))}
                                </Select>
                                <Input
                                  value={nodeData.channel || ''}
                                  onChange={(e) => updateNodeData({ channel: e.target.value })}
                                  placeholder="Or type: general or {channel.name}"
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                  size="sm"
                                />
                              </VStack>
                            </FormControl>

                            {/* Message Content */}
                            <FormControl>
                              <FormLabel color={currentScheme.colors.text}>Message Content (appears above embed)</FormLabel>
                              <Textarea
                                value={nodeData.message || ''}
                                onChange={(e) => updateNodeData({ message: e.target.value })}
                                placeholder="Hello {user.username}! This text appears above the embed..."
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                                minH="100px"
                              />
                            </FormControl>
                            
                            <SimpleGrid columns={2} spacing={4}>
                              <FormControl>
                                <FormLabel color={currentScheme.colors.text}>Embed Title</FormLabel>
                                <Input
                                  value={nodeData.embed?.title || ''}
                                  onChange={(e) => updateNodeData({ 
                                    embed: { ...nodeData.embed, title: e.target.value } 
                                  })}
                                  placeholder="Welcome to {server.name}!"
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                />
                              </FormControl>
                              
                              <FormControl>
                                <FormLabel color={currentScheme.colors.text}>Embed Color</FormLabel>
                                <VStack spacing={2} align="stretch">
                                  <HStack spacing={2}>
                                    <Input
                                      type="color"
                                      value={nodeData.embed?.color || '#5865F2'}
                                      onChange={(e) => updateNodeData({ 
                                        embed: { ...nodeData.embed, color: e.target.value } 
                                      })}
                                      w="60px"
                                      h="40px"
                                      p={1}
                                      bg={currentScheme.colors.background}
                                      borderColor={currentScheme.colors.border}
                                    />
                                    <Input
                                      value={nodeData.embed?.color || ''}
                                      onChange={(e) => updateNodeData({ 
                                        embed: { ...nodeData.embed, color: e.target.value } 
                                      })}
                                      placeholder="#5865F2"
                                      bg={currentScheme.colors.background}
                                      color={currentScheme.colors.text}
                                      borderColor={currentScheme.colors.border}
                                      flex="1"
                                    />
                                  </HStack>
                                  <HStack spacing={1} flexWrap="wrap">
                                    {['#5865F2', '#57F287', '#FEE75C', '#EB459E', '#ED4245', '#FF6B35', '#00ADB5', '#9B59B6'].map((color) => (
                                      <Button
                                        key={color}
                                        size="xs"
                                        bg={color}
                                        w="30px"
                                        h="20px"
                                        minW="30px"
                                        p={0}
                                        onClick={() => updateNodeData({ 
                                          embed: { ...nodeData.embed, color } 
                                        })}
                                        _hover={{ transform: 'scale(1.1)' }}
                                        border="1px solid"
                                        borderColor={currentScheme.colors.border}
                                      />
                                    ))}
                                  </HStack>
                                </VStack>
                              </FormControl>
                            </SimpleGrid>

                            {/* Embed Description */}
                            <FormControl>
                              <FormLabel color={currentScheme.colors.text}>Embed Description</FormLabel>
                              <Textarea
                                value={nodeData.embed?.description || ''}
                                onChange={(e) => updateNodeData({ 
                                  embed: { ...nodeData.embed, description: e.target.value } 
                                })}
                                placeholder="This is the description that appears inside the embed..."
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                                minH="100px"
                              />
                            </FormControl>
                            
                            <SimpleGrid columns={2} spacing={4}>
                              <FormControl>
                                <FormLabel color={currentScheme.colors.text}>Thumbnail URL</FormLabel>
                                <Input
                                  value={nodeData.embed?.thumbnail || ''}
                                  onChange={(e) => updateNodeData({ 
                                    embed: { ...nodeData.embed, thumbnail: e.target.value } 
                                  })}
                                  placeholder="https://example.com/image.png"
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                />
                              </FormControl>
                              
                              <FormControl>
                                <FormLabel color={currentScheme.colors.text}>Image URL</FormLabel>
                                <Input
                                  value={nodeData.embed?.image || ''}
                                  onChange={(e) => updateNodeData({ 
                                    embed: { ...nodeData.embed, image: e.target.value } 
                                  })}
                                  placeholder="https://example.com/image.png"
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                />
                              </FormControl>
                            </SimpleGrid>
                            
                            {/* Author Section */}
                            <Box>
                              <FormLabel color={currentScheme.colors.text}>Author</FormLabel>
                              <VStack spacing={2} align="stretch">
                                <Input
                                  value={nodeData.embed?.author?.name || ''}
                                  onChange={(e) => updateNodeData({ 
                                    embed: { 
                                      ...nodeData.embed, 
                                      author: { ...nodeData.embed?.author, name: e.target.value } 
                                    } 
                                  })}
                                  placeholder="Author name"
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                />
                                <SimpleGrid columns={2} spacing={2}>
                                  <Input
                                    value={nodeData.embed?.author?.url || ''}
                                    onChange={(e) => updateNodeData({ 
                                      embed: { 
                                        ...nodeData.embed, 
                                        author: { ...nodeData.embed?.author, url: e.target.value } 
                                      } 
                                    })}
                                    placeholder="Author URL"
                                    bg={currentScheme.colors.background}
                                    color={currentScheme.colors.text}
                                    borderColor={currentScheme.colors.border}
                                  />
                                  <Input
                                    value={nodeData.embed?.author?.iconUrl || ''}
                                    onChange={(e) => updateNodeData({ 
                                      embed: { 
                                        ...nodeData.embed, 
                                        author: { ...nodeData.embed?.author, iconUrl: e.target.value } 
                                      } 
                                    })}
                                    placeholder="Author icon URL"
                                    bg={currentScheme.colors.background}
                                    color={currentScheme.colors.text}
                                    borderColor={currentScheme.colors.border}
                                  />
                                </SimpleGrid>
                              </VStack>
                            </Box>
                            
                            {/* Footer Section */}
                            <Box>
                              <FormLabel color={currentScheme.colors.text}>Footer</FormLabel>
                              <VStack spacing={2} align="stretch">
                                <Input
                                  value={nodeData.embed?.footer?.text || ''}
                                  onChange={(e) => updateNodeData({ 
                                    embed: { 
                                      ...nodeData.embed, 
                                      footer: { ...nodeData.embed?.footer, text: e.target.value } 
                                    } 
                                  })}
                                  placeholder="Footer text"
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                />
                                <Input
                                  value={nodeData.embed?.footer?.iconUrl || ''}
                                  onChange={(e) => updateNodeData({ 
                                    embed: { 
                                      ...nodeData.embed, 
                                      footer: { ...nodeData.embed?.footer, iconUrl: e.target.value } 
                                    } 
                                  })}
                                  placeholder="Footer icon URL"
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                />
                              </VStack>
                            </Box>
                            
                            {/* Embed Fields */}
                            <Box>
                              <HStack justify="space-between" align="center" mb={2}>
                                <FormLabel color={currentScheme.colors.text} mb={0}>Embed Fields</FormLabel>
                                <Button
                                  size="sm"
                                  leftIcon={<Text>➕</Text>}
                                  onClick={addEmbedField}
                                  colorScheme="blue"
                                >
                                  Add Field
                                </Button>
                              </HStack>
                              <VStack spacing={3} align="stretch">
                                {nodeData.embed?.fields?.map((field, index) => (
                                  <Box
                                    key={index}
                                    p={3}
                                    bg={currentScheme.colors.surface}
                                    borderRadius="md"
                                    border="1px solid"
                                    borderColor={currentScheme.colors.border}
                                  >
                                    <HStack justify="space-between" align="center" mb={2}>
                                      <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                                        Field {index + 1}
                                      </Text>
                                      <IconButton
                                        icon={<Text>🗑️</Text>}
                                        size="xs"
                                        colorScheme="red"
                                        variant="ghost"
                                        onClick={() => removeEmbedField(index)}
                                        aria-label="Remove field"
                                      />
                                    </HStack>
                                    <VStack spacing={2} align="stretch">
                                      <Input
                                        value={field.name}
                                        onChange={(e) => updateEmbedField(index, 'name', e.target.value)}
                                        placeholder="Field name"
                                        bg={currentScheme.colors.background}
                                        color={currentScheme.colors.text}
                                        borderColor={currentScheme.colors.border}
                                      />
                                      <Textarea
                                        value={field.value}
                                        onChange={(e) => updateEmbedField(index, 'value', e.target.value)}
                                        placeholder="Field value"
                                        bg={currentScheme.colors.background}
                                        color={currentScheme.colors.text}
                                        borderColor={currentScheme.colors.border}
                                        minH="80px"
                                      />
                                      <HStack>
                                        <Switch
                                          isChecked={field.inline || false}
                                          onChange={(e) => updateEmbedField(index, 'inline', e.target.checked)}
                                          colorScheme="purple"
                                        />
                                        <Text fontSize="sm" color={currentScheme.colors.text}>
                                          Display inline
                                        </Text>
                                      </HStack>
                                    </VStack>
                                  </Box>
                                ))}
                              </VStack>
                            </Box>
                            
                            <HStack>
                              <Switch
                                isChecked={nodeData.embed?.timestamp || false}
                                onChange={(e) => updateNodeData({ 
                                  embed: { ...nodeData.embed, timestamp: e.target.checked } 
                                })}
                                colorScheme="purple"
                              />
                              <Text fontSize="sm" color={currentScheme.colors.text}>
                                Show current timestamp
                              </Text>
                            </HStack>
                          </VStack>
                        </TabPanel>
                        
                        {/* Embed Preview Tab */}
                        <TabPanel>
                          <VStack spacing={4} align="stretch">
                            <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                              Embed Preview
                            </Text>
                  <Alert status="info" borderRadius="md">
                    <AlertIcon />
                    <AlertDescription fontSize="sm">
                                This shows your message content (above) and embed (below) as they will appear in Discord. Variables will be replaced with actual values when sent.
                    </AlertDescription>
                  </Alert>
                            {renderEmbedPreview()}
                          </VStack>
                        </TabPanel>
                      </TabPanels>
                    </Tabs>
                  ) : (
                    <VStack spacing={4} align="stretch">
                      {/* Non-embed actions configuration */}
                      {(nodeData.actionType === 'sendMessage' || nodeData.actionType === 'sendDM') && (
                        <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel color={currentScheme.colors.text}>Message Content</FormLabel>
                    <Textarea
                      value={nodeData.message || ''}
                      onChange={(e) => updateNodeData({ message: e.target.value })}
                              placeholder="Hello {user.username}! Welcome to {server.name}!"
                      bg={currentScheme.colors.background}
                      color={currentScheme.colors.text}
                      borderColor={currentScheme.colors.border}
                      minH="100px"
                    />
                  </FormControl>
                  
                          {nodeData.actionType !== 'sendDM' && (
                  <FormControl>
                    <FormLabel color={currentScheme.colors.text}>Channel</FormLabel>
                              <VStack spacing={2} align="stretch">
                                <Select
                                  value={nodeData.channel || ''}
                                  onChange={(e) => updateNodeData({ channel: e.target.value })}
                                  placeholder={loadingGuildData ? 'Loading channels...' : 'Select a channel'}
                                  bg={currentScheme.colors.background}
                                  color={currentScheme.colors.text}
                                  borderColor={currentScheme.colors.border}
                                  isDisabled={loadingGuildData}
                                >
                                  {guildData?.channels
                                    .filter(channel => channel.type === 'text')
                                    .map((channel) => (
                                      <option key={channel.id} value={channel.name}>
                                        #{channel.name}
                                      </option>
                                    ))}
                                </Select>
                    <Input
                      value={nodeData.channel || ''}
                      onChange={(e) => updateNodeData({ channel: e.target.value })}
                                  placeholder="Or type: general or {channel.name}"
                      bg={currentScheme.colors.background}
                      color={currentScheme.colors.text}
                      borderColor={currentScheme.colors.border}
                                  size="sm"
                    />
                              </VStack>
                  </FormControl>
                          )}
                        </VStack>
              )}
              
                      {/* Role Actions */}
              {(nodeData.actionType === 'addRole' || nodeData.actionType === 'removeRole') && (
                        <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel color={currentScheme.colors.text}>Role Name</FormLabel>
                            <VStack spacing={2} align="stretch">
                              <Select
                                value={nodeData.role || ''}
                                onChange={(e) => updateNodeData({ role: e.target.value })}
                                placeholder={loadingGuildData ? 'Loading roles...' : 'Select a role'}
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                                isDisabled={loadingGuildData}
                              >
                                {guildData?.roles.map((role) => (
                                  <option key={role.id} value={role.name}>
                                    @{role.name}
                                  </option>
                                ))}
                              </Select>
                  <Input
                    value={nodeData.role || ''}
                    onChange={(e) => updateNodeData({ role: e.target.value })}
                                placeholder="Or type: Member or {user.role}"
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                                size="sm"
                              />
                            </VStack>
                          </FormControl>
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Reason</FormLabel>
                            <Input
                              value={nodeData.reason || ''}
                              onChange={(e) => updateNodeData({ reason: e.target.value })}
                              placeholder="Role updated by bot"
                              bg={currentScheme.colors.background}
                              color={currentScheme.colors.text}
                              borderColor={currentScheme.colors.border}
                            />
                          </FormControl>
                        </VStack>
                      )}

                      {/* Moderation Actions */}
                      {(nodeData.actionType === 'kickUser' || nodeData.actionType === 'banUser') && (
                        <VStack spacing={4} align="stretch">
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>User</FormLabel>
                            <VStack spacing={2} align="stretch">
                              <Select
                                value={nodeData.user || ''}
                                onChange={(e) => updateNodeData({ user: e.target.value })}
                                placeholder={loadingGuildData ? 'Loading members...' : 'Select a user'}
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                                isDisabled={loadingGuildData}
                              >
                                {guildData?.members.map((member) => (
                                  <option key={member.id} value={member.username}>
                                    {member.displayName} (@{member.username})
                                  </option>
                                ))}
                              </Select>
                              <Input
                                value={nodeData.user || ''}
                                onChange={(e) => updateNodeData({ user: e.target.value })}
                                placeholder="Or type: username or {user.id}"
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                                size="sm"
                              />
                            </VStack>
                          </FormControl>
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Reason</FormLabel>
                            <Input
                              value={nodeData.reason || ''}
                              onChange={(e) => updateNodeData({ reason: e.target.value })}
                              placeholder="Violation of server rules"
                              bg={currentScheme.colors.background}
                              color={currentScheme.colors.text}
                              borderColor={currentScheme.colors.border}
                            />
                          </FormControl>
                          {nodeData.actionType === 'banUser' && (
                            <FormControl>
                              <FormLabel color={currentScheme.colors.text}>Delete Message History</FormLabel>
                              <Switch
                                isChecked={nodeData.deleteMessages || false}
                                onChange={(e) => updateNodeData({ deleteMessages: e.target.checked })}
                                colorScheme="purple"
                              />
                            </FormControl>
                          )}
                        </VStack>
                      )}

                      {/* Timeout Actions */}
                      {nodeData.actionType === 'timeoutUser' && (
                        <VStack spacing={4} align="stretch">
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>User</FormLabel>
                            <VStack spacing={2} align="stretch">
                              <Select
                                value={nodeData.user || ''}
                                onChange={(e) => updateNodeData({ user: e.target.value })}
                                placeholder={loadingGuildData ? 'Loading members...' : 'Select a user'}
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                                isDisabled={loadingGuildData}
                              >
                                {guildData?.members.map((member) => (
                                  <option key={member.id} value={member.username}>
                                    {member.displayName} (@{member.username})
                                  </option>
                                ))}
                              </Select>
                              <Input
                                value={nodeData.user || ''}
                                onChange={(e) => updateNodeData({ user: e.target.value })}
                                placeholder="Or type: username or {user.id}"
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                                size="sm"
                              />
                            </VStack>
                          </FormControl>
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Duration (minutes)</FormLabel>
                            <NumberInput
                              value={nodeData.duration || 10}
                              onChange={(valueString) => updateNodeData({ duration: parseInt(valueString) || 10 })}
                              min={1}
                              max={40320}
                            >
                              <NumberInputField
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                              />
                              <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                              </NumberInputStepper>
                            </NumberInput>
                          </FormControl>
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Reason</FormLabel>
                            <Input
                              value={nodeData.reason || ''}
                              onChange={(e) => updateNodeData({ reason: e.target.value })}
                              placeholder="Timeout for spam"
                              bg={currentScheme.colors.background}
                              color={currentScheme.colors.text}
                              borderColor={currentScheme.colors.border}
                            />
                          </FormControl>
                        </VStack>
                      )}

                      {/* Reaction Actions */}
                      {nodeData.actionType === 'addReaction' && (
                        <VStack spacing={4} align="stretch">
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Reaction (emoji)</FormLabel>
                            <Input
                              value={nodeData.reaction || ''}
                              onChange={(e) => updateNodeData({ reaction: e.target.value })}
                              placeholder="👍 or :thumbsup:"
                              bg={currentScheme.colors.background}
                              color={currentScheme.colors.text}
                              borderColor={currentScheme.colors.border}
                            />
                          </FormControl>
                        </VStack>
                      )}

                      {/* Channel Creation Actions */}
                      {nodeData.actionType === 'createChannel' && (
                        <VStack spacing={4} align="stretch">
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Channel Name</FormLabel>
                            <Input
                              value={nodeData.channelName || ''}
                              onChange={(e) => updateNodeData({ channelName: e.target.value })}
                              placeholder="new-channel"
                    bg={currentScheme.colors.background}
                    color={currentScheme.colors.text}
                    borderColor={currentScheme.colors.border}
                  />
                </FormControl>
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Channel Type</FormLabel>
                            <Select
                              value={nodeData.channelType || 'text'}
                              onChange={(e) => updateNodeData({ channelType: e.target.value })}
                              bg={currentScheme.colors.background}
                              color={currentScheme.colors.text}
                              borderColor={currentScheme.colors.border}
                            >
                              <option value="text">Text Channel</option>
                              <option value="voice">Voice Channel</option>
                              <option value="category">Category</option>
                            </Select>
                          </FormControl>
                        </VStack>
                      )}
                    </VStack>
                  )}
                </>
              )}
              
              <Button
                colorScheme="purple"
                                  onClick={() => {
                    // Save the configuration
                    data.actionType = nodeData.actionType;
                    data.message = nodeData.message;
                    data.channel = nodeData.channel;
                    data.role = nodeData.role;
                    data.user = nodeData.user;
                  data.embed = nodeData.embed;
                  data.reason = nodeData.reason;
                  data.duration = nodeData.duration;
                  data.deleteMessages = nodeData.deleteMessages;
                  data.reaction = nodeData.reaction;
                  data.channelName = nodeData.channelName;
                  data.channelType = nodeData.channelType;
                    data.label = nodeData.actionType ? getActionLabel(nodeData.actionType) : 'Action';
                    onClose();
                  }}
                size="lg"
                width="full"
              >
                Save Configuration
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
});

ActionNode.displayName = 'ActionNode';

export default ActionNode; 