import { Client, GatewayIntentBits, PermissionsBitField, ChannelType } from 'discord.js';
import * as fs from 'fs';
import * as path from 'path';
import YAML from 'yaml';

async function runDiagnostics() {
  // Load configuration
  const configPath = path.resolve(process.cwd(), 'config.yml');
  const fileContents = fs.readFileSync(configPath, 'utf8');
  const config = YAML.parse(fileContents);

  const { token, guildId, clientId } = config.bot;

  if (!token || !guildId || !clientId) {
    console.error('Missing required bot configuration:');
    console.error('- Token:', token ? 'Provided' : 'Missing');
    console.error('- Guild ID:', guildId ? 'Provided' : 'Missing');
    console.error('- Client ID:', clientId ? 'Provided' : 'Missing');
    process.exit(1);
  }

  // Create a new Discord client
  const client = new Client({ 
    intents: [
      GatewayIntentBits.Guilds,
      GatewayIntentBits.GuildMembers
    ] 
  });

  try {
    // Log in to Discord
    console.log('Attempting to log in to Discord...');
    await client.login(token);

    console.log('Bot logged in successfully.');
    console.log('Connected to Discord as:', client.user?.tag);

    // Fetch available guilds
    console.log('\nFetching available guilds...');
    const guilds = await client.guilds.fetch();
    
    console.log('Guilds the bot is a member of:');
    guilds.forEach(guild => {
      console.log(`- ${guild.name} (ID: ${guild.id})`);
    });

    // Check if the specified guild is in the list
    const specifiedGuild = guilds.find(g => g.id === guildId);
    if (!specifiedGuild) {
      console.error(`\nERROR: Bot is not a member of the guild with ID ${guildId}`);
      console.error('Possible reasons:');
      console.error('1. Bot has not been invited to the server');
      console.error('2. Guild ID is incorrect');
      console.error('3. Bot was removed from the server');
      process.exit(1);
    }

    // Fetch the guild
    console.log(`\nAttempting to fetch guild with ID: ${guildId}`);
    const guild = await client.guilds.fetch(guildId);

    console.log('Guild fetched successfully:');
    console.log('- Name:', guild.name);
    console.log('- Member Count:', guild.memberCount);

    // Check bot's permissions in the guild
    const botMember = guild.members.me;
    if (!botMember) {
      console.error('Bot is not a member of the guild!');
      process.exit(1);
    }

    console.log('\nBot Permissions:');
    const permissions = botMember.permissions;
    const requiredPermissions = [
      'ViewChannels',
      'ManageChannels',
      'SendMessages'
    ];

    requiredPermissions.forEach(perm => {
      console.log(`- ${perm}: ${permissions.has(PermissionsBitField.Flags[perm]) ? '✓' : '✗'}`);
    });

    // Attempt to fetch channels
    console.log('\nAttempting to fetch channels...');
    const channels = await guild.channels.fetch();
    
    console.log('Channels fetched successfully:');
    channels.forEach(channel => {
      if (channel) {
        console.log(`- ${channel.name} (${ChannelType[channel.type]})`);
      }
    });

  } catch (error) {
    console.error('Diagnostic Error:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    process.exit(1);
  } finally {
    // Properly close the client
    client.destroy();
  }
}

runDiagnostics(); 