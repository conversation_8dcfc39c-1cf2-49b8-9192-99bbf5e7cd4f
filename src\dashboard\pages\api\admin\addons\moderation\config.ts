import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';
import fs from 'fs';
import path from 'path';
import YAML from 'yaml';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate the request
  const session = await getServerSession(req, res, authOptions);
  if (!session || !(session.user as any).isAdmin) {
    return res.status(403).json({ error: 'Unauthorized' });
  }

  // Handle GET requests to load current configuration
  if (req.method === 'GET') {
    try {
      const configPath = path.join(process.cwd(), 'src', 'addons', 'moderation', 'config.yml');
      
      let config = {
        raidProtection: { enabled: true, joinThreshold: 5, timeWindowMs: 10000, mitigationAction: 'ban' },
        spamPrevention: { enabled: true, msgThreshold: 5, timeWindowMs: 10000, action: 'timeout' },
        logging: { enabled: true, channelId: '' }
      };

      try {
        const configRaw = fs.readFileSync(configPath, 'utf8');
        const parsedConfig = YAML.parse(configRaw);
        
        // Merge with defaults to ensure all fields are present
        config = {
          raidProtection: { ...config.raidProtection, ...parsedConfig.raidProtection },
          spamPrevention: { ...config.spamPrevention, ...parsedConfig.spamPrevention },
          logging: { ...config.logging, ...parsedConfig.logging }
        };
      } catch (readError) {
        console.warn('Could not read config file, using defaults', readError);
      }

      return res.status(200).json(config);
    } catch (error) {
      console.error('Error loading moderation config:', error);
      return res.status(500).json({ error: 'Failed to load configuration' });
    }
  }

  // Only allow POST requests for saving
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate the incoming configuration
    const config = req.body;
    
    // Validate raid protection settings
    if (config.raidProtection) {
      if (typeof config.raidProtection.enabled !== 'boolean') {
        return res.status(400).json({ error: 'Invalid raid protection enabled flag' });
      }
      if (typeof config.raidProtection.joinThreshold !== 'number' || 
          config.raidProtection.joinThreshold < 2 || 
          config.raidProtection.joinThreshold > 20) {
        return res.status(400).json({ error: 'Invalid join threshold' });
      }
      if (typeof config.raidProtection.timeWindowMs !== 'number' || 
          config.raidProtection.timeWindowMs < 1000 || 
          config.raidProtection.timeWindowMs > 60000) {
        return res.status(400).json({ error: 'Invalid time window' });
      }
      if (!['ban', 'kick'].includes(config.raidProtection.mitigationAction)) {
        return res.status(400).json({ error: 'Invalid mitigation action' });
      }
    }

    // Validate spam prevention settings
    if (config.spamPrevention) {
      if (typeof config.spamPrevention.enabled !== 'boolean') {
        return res.status(400).json({ error: 'Invalid spam prevention enabled flag' });
      }
      if (typeof config.spamPrevention.msgThreshold !== 'number' || 
          config.spamPrevention.msgThreshold < 2 || 
          config.spamPrevention.msgThreshold > 20) {
        return res.status(400).json({ error: 'Invalid message threshold' });
      }
      if (typeof config.spamPrevention.timeWindowMs !== 'number' || 
          config.spamPrevention.timeWindowMs < 1000 || 
          config.spamPrevention.timeWindowMs > 60000) {
        return res.status(400).json({ error: 'Invalid time window' });
      }
      if (!['timeout', 'kick'].includes(config.spamPrevention.action)) {
        return res.status(400).json({ error: 'Invalid spam action' });
      }
    }

    // Validate logging settings
    if (config.logging) {
      if (typeof config.logging.enabled !== 'boolean') {
        return res.status(400).json({ error: 'Invalid logging enabled flag' });
      }
      // Optional channel validation could be added here if needed
    }

    // Path to the moderation addon config file
    const configPath = path.join(process.cwd(), 'src', 'addons', 'moderation', 'config.yml');

    // Read existing config to preserve any additional settings
    let existingConfig = {};
    try {
      const existingConfigRaw = fs.readFileSync(configPath, 'utf8');
      existingConfig = YAML.parse(existingConfigRaw);
    } catch (readError) {
      console.warn('Could not read existing config, creating new', readError);
    }

    // Merge new config with existing config
    const updatedConfig = {
      ...existingConfig,
      raidProtection: config.raidProtection,
      spamPrevention: config.spamPrevention,
      logging: config.logging
    };

    // Write updated config back to file
    fs.writeFileSync(configPath, YAML.stringify(updatedConfig), 'utf8');

    // Trigger addon reload (optional, depends on your addon management system)
    try {
      await fetch('http://localhost:3000/api/admin/addons/reload', { 
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (reloadError) {
      console.warn('Failed to reload addon', reloadError);
    }

    return res.status(200).json({ message: 'Moderation configuration updated successfully' });

  } catch (error) {
    console.error('Error updating moderation config:', error);
    return res.status(500).json({ error: 'Failed to update configuration' });
  }
} 