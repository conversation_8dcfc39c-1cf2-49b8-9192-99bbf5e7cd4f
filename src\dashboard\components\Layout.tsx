import Navbar from './Navbar';
import Sidebar from './Sidebar';
import { useTheme } from '../contexts/ThemeContext';
import React from 'react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { currentScheme } = useTheme();

  // Dynamic styling functions
  const getBackgroundGradientStyle = () => {
    return {
      backgroundImage: `
        radial-gradient(circle at 15% 50%, ${currentScheme.colors.primary}15 0%, transparent 25%),
        radial-gradient(circle at 85% 30%, ${currentScheme.colors.accent}15 0%, transparent 25%)
      `,
    };
  };

  const getMainContainerClasses = () => {
    return `
      relative z-10 flex flex-col min-h-screen
      transition-colors duration-300
    `;
  };

  const getContentAreaClasses = () => {
    return `
      flex flex-1 relative pt-16
      sm:pl-64 md:pl-72 lg:pl-80
    `;
  };

  const getMainContentClasses = () => {
    return `
      flex-1 p-4 md:p-8 max-w-full 
      transition-all relative
    `;
  };

  return (
    <div 
      className="min-h-screen relative overflow-hidden"
      style={{
        backgroundColor: currentScheme.colors.background,
      }}
    >
      {/* Background gradient effects */}
      <div 
        className="fixed inset-0 z-0"
        style={getBackgroundGradientStyle()}
      />
      <div className="fixed inset-0 z-0 backdrop-blur-[100px]" />

      {/* Main content container */}
      <div className={getMainContainerClasses()}>
        {/* Navbar */}
        <div className="fixed top-0 left-0 right-0 z-30">
          <Navbar />
        </div>

        {/* Content area with sidebar */}
        <div className={getContentAreaClasses()}>
          {/* Sidebar */}
          <div className="fixed top-16 bottom-0 left-0 z-20 w-64 sm:w-72 lg:w-80">
            <Sidebar />
          </div>

          {/* Main content */}
          <div className={getMainContentClasses()}>
            {/* Subtle gradient overlay */}
            <div 
              className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent -z-10 pointer-events-none"
            />

            {/* Container for content */}
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout; 