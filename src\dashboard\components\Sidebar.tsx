import { FiServer, FiSettings, FiUsers, FiPackage, FiHelpCircle, FiMonitor, FiHome, FiDatabase, FiActivity, FiBox, FiCommand, FiChevronDown, FiAlertCircle } from 'react-icons/fi';
import { FaFileAlt, FaFlask } from 'react-icons/fa';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import useGuildInfo from '../hooks/useGuildInfo';
import { useTheme } from '../contexts/ThemeContext';

// Import package.json version
const BOT_VERSION = '1.0.0'; // You can update this manually or import from package.json

const DEVELOPER_ID = '933023999770918932';

interface ApplicationItem {
  id: string;
  title: string;
  enabled: boolean;
  settings?: {
    openingSchedule?: {
      enabled: boolean;
      startDate: string;
      endDate: string;
    };
  };
}

export default function Sidebar() {
  const { data: session } = useSession();
  const router = useRouter();
  const isAdmin = (session?.user as any)?.isAdmin;
  const userId = (session?.user as any)?.id;
  const [isAdminExpanded, setIsAdminExpanded] = useState(false);
  const [isApplicationsExpanded, setIsApplicationsExpanded] = useState(false);
  const [applicationsOpen, setApplicationsOpen] = useState(false);
  const [openApplications, setOpenApplications] = useState<ApplicationItem[]>([]);
  const { displayName } = useGuildInfo();
  const { currentScheme } = useTheme();

  useEffect(() => {
    const fetchApplicationStatus = async () => {
      try {
        console.group('Sidebar Fetch Diagnostics');
        console.log('Starting application status fetch...');

        // Fetch application config
        const configRes = await fetch('/api/applications/config', {
          credentials: 'include'
        });

        if (!configRes.ok) {
          console.warn('Failed to fetch application config:', configRes.status);
          setApplicationsOpen(false);
          setOpenApplications([]);
          return;
        }

        const configData = await configRes.json();
        setApplicationsOpen(configData.isOpen || configData.open);

        // Fetch applications only if config indicates they're open
        if (configData.isOpen || configData.open) {
          try {
            const appsRes = await fetch('/api/admin/applications-builder', {
              credentials: 'include'
            });

            if (!appsRes.ok) {
              console.warn('Failed to fetch applications:', appsRes.status);
              setOpenApplications([]);
              return;
            }

            const appsData = await appsRes.json();
            setOpenApplications(appsData);
          } catch (appsError) {
            console.error('Error fetching applications:', appsError);
            setOpenApplications([]);
          }
        }

        // Attempt to fetch Discord channels with error handling
        try {
          const channelsRes = await fetch('/api/discord/channels', {
            credentials: 'include'
          });

          if (!channelsRes.ok) {
            console.warn('Failed to fetch Discord channels:', {
              status: channelsRes.status,
              statusText: channelsRes.statusText
            });
            
            // Try to get error details
            const errorDetails = await channelsRes.json().catch(() => ({}));
            console.warn('Channel fetch error details:', errorDetails);
          }
        } catch (channelsError) {
          console.error('Unexpected error fetching Discord channels:', channelsError);
        }

        console.groupEnd();
      } catch (error) {
        console.error('Comprehensive fetch error:', error);
        setApplicationsOpen(false);
        setOpenApplications([]);
      }
    };

    fetchApplicationStatus();
  }, []);

  const menuItems = [
    { name: 'Overview', icon: FiHome, href: '/overview' },
    { name: 'Applications', icon: FiPackage, href: '/applications' },
    { name: 'Tickets', icon: FiHelpCircle, href: '/tickets' },
    { name: 'Game Servers', icon: FiMonitor, href: '/gameservers' },
  ];

  const adminQuickLinks = [
    { name: 'Server Management', href: '/admin/guilds', icon: FiSettings },
    { name: 'Addons', href: '/admin/addons', icon: FiBox },
    { name: 'Errors', href: '/admin/errors', icon: FiAlertCircle },
  ];

  const isActive = (href: string) => {
    if (href === '/overview') {
      return router.pathname === href;
    }
    return router.pathname.startsWith(href);
  };

  // Dynamic styling functions
  const getMenuItemStyles = (href: string, isExpanded = false) => {
    const active = isActive(href);
    return `
      flex items-center px-4 py-3 text-sm font-medium
      transition-all duration-200 group
      ${active || isExpanded 
        ? `text-[${currentScheme.colors.text}]` 
        : `text-[${currentScheme.colors.textSecondary}]`
      }
      ${active 
        ? `bg-[${currentScheme.colors.primary}30] border-r-2 border-[${currentScheme.colors.primary}]` 
        : 'bg-transparent border-r-2 border-transparent'
      }
      hover:bg-[${currentScheme.colors.primary}40]
      hover:translate-x-1
      rounded-r-md
    `;
  };

  const getBackgroundGradientStyle = () => {
    return {
      background: `linear-gradient(180deg, ${currentScheme.colors.primary}15 0%, ${currentScheme.colors.accent}15 100%)`,
    };
  };

  const getIconStyles = (active = false, isExpanded = false) => {
    return `
      w-5 h-5 mr-3 transition-transform 
      group-hover:scale-110
      ${active || isExpanded 
        ? `text-[${currentScheme.colors.primary}]` 
        : `text-[${currentScheme.colors.textSecondary}]`
      }
    `;
  };

  const getSubItemStyles = (href: string) => {
    const active = isActive(href);
    return `
      flex items-center px-4 py-2 text-xs font-medium
      transition-all duration-200 group
      ${active 
        ? `text-[${currentScheme.colors.text}] bg-[${currentScheme.colors.primary}20]`
        : `text-[${currentScheme.colors.textSecondary}] bg-transparent`
      }
      hover:bg-[${currentScheme.colors.surface}]
      hover:translate-x-0.5
      rounded-md
    `;
  };

  return (
    <nav 
      className="h-full relative w-64 lg:w-72"
      style={{
        backgroundColor: currentScheme.colors.surface,
        borderRight: `1px solid ${currentScheme.colors.border}`,
      }}
    >
      {/* Background gradient */}
      <div 
        className="absolute inset-0 -z-10"
        style={getBackgroundGradientStyle()}
      />

      <div className="flex flex-col h-full py-8 space-y-2">
        {menuItems.map((item) => {
          const active = isActive(item.href);
          
          if (item.name === 'Applications') {
            return (
              <div key={item.name} className="relative">
                <div 
                  className={getMenuItemStyles(item.href, isApplicationsExpanded)}
                  onClick={() => setIsApplicationsExpanded(!isApplicationsExpanded)}
                >
                  <item.icon 
                    className={getIconStyles(active, isApplicationsExpanded)} 
                  />
                  <span 
                    className={`
                      hidden lg:block flex-1
                      ${active 
                        ? `bg-gradient-to-r from-[${currentScheme.colors.primaryLight}] to-[${currentScheme.colors.accent}] bg-clip-text text-transparent` 
                        : ''
                      }
                    `}
                  >
                    {item.name}
                  </span>
                  <FiChevronDown 
                    className={`
                      w-4 h-4 ml-2 transition-transform opacity-60
                      ${isApplicationsExpanded ? 'rotate-180' : 'rotate-0'}
                    `} 
                  />
                </div>

                {isApplicationsExpanded && (
                  <div className="pl-4 py-2 space-y-1">
                    {openApplications.map((app: ApplicationItem) => (
                      <Link
                        key={app.id}
                        href={`/applications/${app.id}`}
                        className={getSubItemStyles(`/applications/${app.id}`)}
                      >
                        <span className="hidden lg:block">
                          {app.title}
                        </span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            );
          }

          return (
            <Link
              key={item.name}
              href={item.href}
              className={getMenuItemStyles(item.href)}
            >
              <item.icon 
                className={getIconStyles(active)} 
              />
              <span 
                className={`
                  hidden lg:block flex-1
                  ${active 
                    ? `bg-gradient-to-r from-[${currentScheme.colors.primaryLight}] to-[${currentScheme.colors.accent}] bg-clip-text text-transparent` 
                    : ''
                  }
                `}
              >
                {item.name}
              </span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
} 