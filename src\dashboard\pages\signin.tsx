// @ts-nocheck
import { signIn, signOut, useSession } from 'next-auth/react';
import { FaD<PERSON>rd, FaCode, FaRobot, FaServer, FaCog, FaBolt } from 'react-icons/fa';
import { useEffect, useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';

// Quotes for random selection
const quotes = [
  "Welcome to 404 Chill Not Found - Where errors take a break!",
  "Join our community of chill developers and gamers",
  "Relax, you're in the right place",
  "404 Error? More like 404 Fun Found!",
  "Where coding meets community"
];

export default function SignIn() {
  const { currentScheme } = useTheme();
  const [quote, setQuote] = useState(quotes[0]);
  const { data: session } = useSession();

  useEffect(() => {
    // Only update the quote after the component has mounted on the client
    const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
    setQuote(randomQuote);
  }, []);

  // Dynamic styling functions
  const getBackgroundStyle = () => {
    return {
      background: `
        radial-gradient(circle at 15% 50%, ${currentScheme.colors.primary}15 0%, transparent 25%),
        radial-gradient(circle at 85% 30%, ${currentScheme.colors.accent}15 0%, transparent 25%)
      `,
    };
  };

  // Floating and pulse animations using Tailwind
  const getFloatingAnimation = (delay: number) => {
    return `
      absolute opacity-10 
      animate-[float_3s_ease-in-out_infinite_${delay}s]
    `;
  };

  const getPulseAnimation = () => {
    return 'animate-[pulse_3s_ease-in-out_infinite]';
  };

  const getButtonStyle = (isSignOut = false) => {
    return `
      w-full py-3 rounded-lg 
      flex items-center justify-center 
      space-x-2 
      transition-all duration-300
      ${isSignOut 
        ? 'bg-red-500 hover:bg-red-600 text-white' 
        : 'bg-[#5865F2] hover:bg-[#4752C4] text-white'
      }
      hover:translate-y-[-2px]
      hover:shadow-[0_0_20px_rgba(88,101,242,0.6)]
      active:translate-y-0
      active:shadow-[0_0_10px_rgba(88,101,242,0.4)]
    `;
  };

  return (
    <div 
      className="min-h-screen flex items-center justify-center relative overflow-hidden"
      style={getBackgroundStyle()}
    >
      {/* Background floating elements */}
      <div 
        className={`${getFloatingAnimation(0)} top-[10%] left-[5%]`}
      >
        <FaCode className="w-20 h-20" />
      </div>
      <div 
        className={`${getFloatingAnimation(0.5)} bottom-[15%] right-[10%]`}
      >
        <FaCog className="w-16 h-16" />
      </div>
      <div 
        className={`${getFloatingAnimation(1)} top-[20%] right-[15%]`}
      >
        <FaBolt className="w-14 h-14" />
      </div>
      <div 
        className={`${getFloatingAnimation(1.5)} bottom-[20%] left-[15%]`}
      >
        <FaServer className="w-18 h-18" />
      </div>

      {/* Main content */}
      <div 
        className="
          bg-gray-800 
          p-8 
          rounded-xl 
          shadow-2xl 
          max-w-md 
          w-full 
          relative 
          z-10
          space-y-8
          text-center
        "
      >
        {/* Robot Icon */}
        <div className={`flex justify-center ${getPulseAnimation()}`}>
          <FaRobot 
            className="w-20 h-20 text-[#5865F2]" 
          />
        </div>
        
        {/* Title */}
        <h1 
          className="
            text-3xl 
            font-bold 
            bg-gradient-to-r 
            from-[#5865F2] 
            to-purple-500 
            bg-clip-text 
            text-transparent
          "
        >
          404 Bot Dashboard
        </h1>
        
        {/* Quote */}
        <p 
          className="
            text-lg 
            text-gray-300 
            transition-all 
            duration-500
          "
        >
          {quote}
        </p>

        {/* Sign In/Out Button */}
        {session ? (
          <button
            onClick={() => signOut({ callbackUrl: '/signin' })}
            className={getButtonStyle(true)}
          >
            <FaDiscord className="w-5 h-5 mr-2" />
            Sign Out
          </button>
        ) : (
          <button
            onClick={() => signIn('discord', { callbackUrl: '/overview' })}
            className={getButtonStyle()}
          >
            <FaDiscord className="w-5 h-5 mr-2" />
            Sign in with Discord
          </button>
        )}
      </div>
    </div>
  );
}
