import React from 'react';
import NextLink from 'next/link';
import { AdminCardConfig } from '../config/adminCards';
import { useSession } from 'next-auth/react';
import { useTheme } from '../contexts/ThemeContext';

interface AdminCardProps {
  card: AdminCardConfig;
  userPermissions?: string[];
}

export const AdminCard: React.FC<AdminCardProps> = ({
  card,
  userPermissions = [],
}) => {
  const { data: session } = useSession();
  const { currentScheme } = useTheme();
  
  // Developer-only check - hardcoded for security
  const DEVELOPER_ID = '933023999770918932';
  const isDeveloper = session?.user?.id === DEVELOPER_ID;
  
  const hasPermission = !card.requiresPermission || 
    card.requiresPermission.some(permission => userPermissions.includes(permission));

  // Hide developer-only cards from non-developers
  if (card.developerOnly && !isDeveloper) {
    return null;
  }

  if (!hasPermission) return null;

  // Dynamic class generation functions
  const getBackgroundStyle = () => {
    if (card.gradient) {
      return `bg-gradient-to-r from-[${card.gradient.from}] to-[${card.gradient.to}]`;
    }
    return currentScheme.colors.surface;
  };

  const getBackgroundPatternStyle = () => {
    if (card.gradient?.from) {
      return `before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,transparent,transparent_10px,${card.gradient.from}10_10px,${card.gradient.from}10_20px)] before:opacity-5 before:pointer-events-none`;
    }
    return '';
  };

  const getHoverStyles = () => {
    return `
      hover:translate-y-[-4px]
      hover:shadow-[0_6px_25px_${currentScheme.colors.primary}60]
      hover:border-[${currentScheme.colors.primary}]
    `;
  };

  return (
    <NextLink 
      href={card.href} 
      className={`
        block relative p-6 rounded-xl border transition-all duration-300 group
        ${getBackgroundStyle()}
        border-[${currentScheme.colors.border}]
        shadow-[0_4px_20px_${currentScheme.colors.background}80]
        backdrop-blur-[10px]
        ${getHoverStyles()}
        ${getBackgroundPatternStyle()}
        sm:max-w-sm md:max-w-md lg:max-w-lg
      `}
    >
      <div className="flex flex-col space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <card.icon 
              className={`w-6 h-6 text-${card.color}-300`} 
            />
            <h2 
              className="text-lg font-bold" 
              style={{ color: currentScheme.colors.text }}
            >
              {card.title}
            </h2>
          </div>
          {card.badge && (
            <span 
              className={`
                px-2 py-1 rounded-full text-sm 
                bg-${card.badge.color}-500 text-white
              `}
            >
              {card.badge.text}
            </span>
          )}
        </div>

        <p 
          className="text-sm line-clamp-2" 
          style={{ color: currentScheme.colors.textSecondary }}
        >
          {card.description}
        </p>

        {card.stats && (
          <div 
            className={`grid grid-cols-${card.stats.length} gap-4 pt-2`}
          >
            {card.stats.map((stat, index) => (
              <div key={index} className="space-y-1">
                <p 
                  className="text-xs" 
                  style={{ color: currentScheme.colors.textSecondary }}
                >
                  {stat.label}
                </p>
                <p 
                  className={`text-xl text-${card.color}-300`}
                >
                  {stat.value}
                </p>
                {stat.change && (
                  <p 
                    className={`
                      text-xs flex items-center 
                      ${stat.change.isIncrease ? 'text-green-500' : 'text-red-500'}
                    `}
                  >
                    {stat.change.isIncrease ? '▲' : '▼'}
                    {stat.change.value}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </NextLink>
  );
}; 