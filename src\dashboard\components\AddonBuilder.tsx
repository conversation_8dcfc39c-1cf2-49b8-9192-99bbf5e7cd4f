import React, { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { 
  FiSettings as FiPlus, 
  FiTrash2, 
  FiCommand as FiCode, 
  FiTrendingUp as FiRocket, 
  FiCopy as FiDownload, 
  FiAlertCircle as <PERSON>Eye, 
  FiChevronDown as FiArrowLeft 
} from 'react-icons/fi';

interface AddonConfig {
  name: string;
  version: string;
  description: string;
  author: string;
  commands: CommandConfig[];
  events: EventConfig[];
  settings: {
    embedColor: string;
    [key: string]: any;
  };
  database?: {
    collections: string[];
  };
}

interface CommandConfig {
  name: string;
  description: string;
  type: 'slash' | 'context-menu';
  options?: SlashCommandOption[];
  permissions?: string[];
  cooldown?: number;
  enabled: boolean;
  code: string;
}

interface EventConfig {
  name: string;
  once: boolean;
  code: string;
}

interface SlashCommandOption {
  name: string;
  description: string;
  type: 'string' | 'integer' | 'boolean' | 'user' | 'channel' | 'role' | 'mentionable' | 'number' | 'attachment';
  required: boolean;
  choices?: { name: string; value: string | number }[];
}

const defaultCommand: CommandConfig = {
  name: '',
  description: '',
  type: 'slash',
  permissions: [],
  cooldown: 3000,
  enabled: true,
  code: `// Your command code here
await interaction.reply({
  content: 'Hello from your new command!',
  ephemeral: true
});`
};

const defaultEvent: EventConfig = {
  name: 'ready',
  once: true,
  code: `// Your event code here
console.log('Bot is ready!');`
};

const eventTypes = [
  'ready', 'messageCreate', 'interactionCreate', 'guildMemberAdd', 
  'guildMemberRemove', 'voiceStateUpdate', 'messageReactionAdd', 
  'messageReactionRemove', 'channelCreate', 'channelDelete'
];

const optionTypes = [
  'string', 'integer', 'boolean', 'user', 'channel', 'role', 
  'mentionable', 'number', 'attachment'
];

export default function AddonBuilder() {
  const { data: session } = useSession();
  const router = useRouter();
  const [previewCode, setPreviewCode] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);

  const [config, setConfig] = useState<AddonConfig>({
    name: '',
    version: '1.0.0',
    description: '',
    author: (session?.user as any)?.name || '',
    commands: [],
    events: [],
    settings: {
      embedColor: '#0099FF'
    }
  });

  const validateConfig = useCallback((): string[] => {
    const errors: string[] = [];

    if (!config.name || config.name.length < 2) {
      errors.push('Addon name must be at least 2 characters long');
    }

    if (!/^[a-z0-9-]+$/.test(config.name)) {
      errors.push('Addon name must contain only lowercase letters, numbers, and hyphens');
    }

    if (!config.version || !/^\d+\.\d+\.\d+$/.test(config.version)) {
      errors.push('Version must be in semver format (e.g., 1.0.0)');
    }

    if (!config.description || config.description.length < 10) {
      errors.push('Description must be at least 10 characters long');
    }

    if (!config.author || config.author.length < 2) {
      errors.push('Author name must be at least 2 characters long');
    }

    if (!config.settings?.embedColor || !/^#[0-9a-fA-F]{6}$/.test(config.settings.embedColor)) {
      errors.push('Embed color must be a valid hex color (e.g., #0099FF)');
    }

    config.commands.forEach((cmd, index) => {
      if (!cmd.name || !/^[a-z0-9-]+$/.test(cmd.name)) {
        errors.push(`Command ${index + 1}: Invalid name format`);
      }

      if (!cmd.description || cmd.description.length < 1) {
        errors.push(`Command ${index + 1}: Description is required`);
      }

      if (!cmd.code || cmd.code.trim().length < 10) {
        errors.push(`Command ${index + 1}: Code implementation is required`);
      }
    });

    config.events.forEach((event, index) => {
      if (!event.name) {
        errors.push(`Event ${index + 1}: Name is required`);
      }

      if (!event.code || event.code.trim().length < 5) {
        errors.push(`Event ${index + 1}: Code implementation is required`);
      }
    });

    return errors;
  }, [config]);

  const updateConfig = useCallback((field: keyof AddonConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  }, []);

  const addCommand = useCallback(() => {
    setConfig(prev => ({
      ...prev,
      commands: [...prev.commands, { ...defaultCommand }]
    }));
  }, []);

  const updateCommand = useCallback((index: number, field: keyof CommandConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      commands: prev.commands.map((cmd, i) => 
        i === index ? { ...cmd, [field]: value } : cmd
      )
    }));
  }, []);

  const removeCommand = useCallback((index: number) => {
    setConfig(prev => ({
      ...prev,
      commands: prev.commands.filter((_, i) => i !== index)
    }));
  }, []);

  const addEvent = useCallback(() => {
    setConfig(prev => ({
      ...prev,
      events: [...prev.events, { ...defaultEvent }]
    }));
  }, []);

  const updateEvent = useCallback((index: number, field: keyof EventConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      events: prev.events.map((event, i) => 
        i === index ? { ...event, [field]: value } : event
      )
    }));
  }, []);

  const removeEvent = useCallback((index: number) => {
    setConfig(prev => ({
      ...prev,
      events: prev.events.filter((_, i) => i !== index)
    }));
  }, []);

  const handlePreview = useCallback(() => {
    const errors = validateConfig();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    const preview = `// ${config.name} - Generated Addon Preview
// This is a preview of your addon's main structure

export default {
  info: {
    name: "${config.name}",
    version: "${config.version}",
    description: "${config.description}",
    author: "${config.author}"
  },

  commands: ${config.commands.length} command${config.commands.length !== 1 ? 's' : ''},
  events: ${config.events.length} event${config.events.length !== 1 ? 's' : ''},
  
  settings: {
    embedColor: "${config.settings.embedColor}"
  }
};

// Commands: ${config.commands.map(cmd => cmd.name).join(', ') || 'None'}
// Events: ${config.events.map(event => event.name).join(', ') || 'None'}
`;

    setPreviewCode(preview);
  }, [config, validateConfig]);

  const handleCreate = useCallback(async () => {
    const errors = validateConfig();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setIsCreating(true);
    setValidationErrors([]);

    try {
      const response = await fetch('/api/experimental/addon-builder/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create addon');
      }

      // Reset form
      setConfig({
        name: '',
        version: '1.0.0',
        description: '',
        author: (session?.user as any)?.name || '',
        commands: [],
        events: [],
        settings: {
          embedColor: '#0099FF'
        }
      });

    } catch (error) {
      console.error('Error creating addon:', error);
    } finally {
      setIsCreating(false);
    }
  }, [config, validateConfig, session]);

  const handleGoBack = useCallback(() => {
    // Check if user has made any changes
    const hasChanges = config.name.trim() !== '' || 
                      config.description.trim() !== '' || 
                      config.commands.length > 0 || 
                      config.events.length > 0 ||
                      config.author !== ((session?.user as any)?.name || '');

    if (hasChanges) {
      // Implement a confirmation dialog or modal here
      router.push('/admin/addons');
    } else {
      router.push('/admin/addons');
    }
  }, [config, session, router]);

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold mb-2 text-white">🛠️ Addon Builder</h1>
        <p className="text-gray-400 mb-4">
          Create custom addons for your Discord bot with a visual interface
        </p>
        <div className="flex justify-center space-x-3">
          <button 
            onClick={handleGoBack}
            className="flex items-center px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition"
          >
            <FiArrowLeft className="mr-2" /> Go Back
          </button>
          <button 
            onClick={() => {/* Open template modal */}}
            className="flex items-center px-4 py-2 bg-purple-700 text-white rounded hover:bg-purple-600 transition"
          >
            <FiCode className="mr-2" /> Start from Template
          </button>
        </div>
      </div>

      {validationErrors.length > 0 && (
        <div className="bg-red-900 bg-opacity-20 border border-red-600 rounded-lg p-4 mb-6">
          <h3 className="text-red-300 font-semibold mb-2">Validation Errors:</h3>
          <ul className="space-y-1">
            {validationErrors.map((error, index) => (
              <li key={index} className="text-red-200 text-sm">• {error}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="bg-gray-800 rounded-lg p-6">
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div>
            <label htmlFor="addon-name" className="block text-sm font-medium text-gray-300 mb-2">
              Addon Name
            </label>
            <input
              id="addon-name"
              type="text"
              value={config.name}
              onChange={(e) => updateConfig('name', e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
              placeholder="my-awesome-addon"
              className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
            <p className="text-xs text-gray-500 mt-1">
              Only lowercase letters, numbers, and hyphens allowed
            </p>
          </div>

          <div>
            <label htmlFor="addon-version" className="block text-sm font-medium text-gray-300 mb-2">
              Version
            </label>
            <input
              id="addon-version"
              type="text"
              value={config.version}
              onChange={(e) => updateConfig('version', e.target.value)}
              placeholder="1.0.0"
              className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>

          <div>
            <label htmlFor="addon-author" className="block text-sm font-medium text-gray-300 mb-2">
              Author
            </label>
            <input
              id="addon-author"
              type="text"
              value={config.author}
              onChange={(e) => updateConfig('author', e.target.value)}
              placeholder="Your Name"
              className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>

          <div>
            <label htmlFor="embed-color" className="block text-sm font-medium text-gray-300 mb-2">
              Embed Color
            </label>
            <div className="flex items-center space-x-2">
              <input
                id="embed-color"
                type="color"
                value={config.settings.embedColor}
                onChange={(e) => updateConfig('settings', { ...config.settings, embedColor: e.target.value })}
                className="w-16 h-10 bg-gray-700 rounded-md"
              />
              <input
                type="text"
                value={config.settings.embedColor}
                onChange={(e) => updateConfig('settings', { ...config.settings, embedColor: e.target.value })}
                placeholder="#0099FF"
                className="flex-1 bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>
          </div>
        </div>

        <div className="mb-6">
          <label htmlFor="addon-description" className="block text-sm font-medium text-gray-300 mb-2">
            Description
          </label>
          <textarea
            id="addon-description"
            value={config.description}
            onChange={(e) => updateConfig('description', e.target.value)}
            placeholder="A brief description of what your addon does..."
            rows={4}
            className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none resize-y"
          />
        </div>

        {/* Commands Section */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">Commands</h2>
            <button 
              onClick={addCommand}
              className="flex items-center px-3 py-2 bg-blue-700 text-white rounded hover:bg-blue-600 transition"
            >
              <FiPlus className="mr-2" /> Add Command
            </button>
          </div>

          {config.commands.length === 0 ? (
            <div className="bg-gray-700 rounded-lg p-4 text-center text-gray-400">
              No commands yet. Add your first command to get started!
            </div>
          ) : (
            <div className="space-y-4">
              {config.commands.map((command, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-white">
                      {command.name || `Command ${index + 1}`}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <span 
                        className={`px-2 py-1 rounded text-xs ${
                          command.enabled ? 'bg-green-600 text-white' : 'bg-gray-500 text-gray-300'
                        }`}
                      >
                        {command.enabled ? 'Enabled' : 'Disabled'}
                      </span>
                      <button
                        onClick={() => removeCommand(index)}
                        className="text-red-500 hover:text-red-400 transition"
                      >
                        <FiTrash2 />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Command Name
                      </label>
                      <input
                        type="text"
                        value={command.name}
                        onChange={(e) => updateCommand(index, 'name', e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
                        placeholder="ping"
                        className="w-full bg-gray-800 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Cooldown (ms)
                      </label>
                      <input
                        type="number"
                        value={command.cooldown}
                        onChange={(e) => updateCommand(index, 'cooldown', Number(e.target.value))}
                        min={1000}
                        max={300000}
                        className="w-full bg-gray-800 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Description
                    </label>
                    <input
                      type="text"
                      value={command.description}
                      onChange={(e) => updateCommand(index, 'description', e.target.value)}
                      placeholder="Shows bot ping"
                      className="w-full bg-gray-800 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                    />
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Command Code
                    </label>
                    <textarea
                      value={command.code}
                      onChange={(e) => updateCommand(index, 'code', e.target.value)}
                      placeholder="// Your command implementation here"
                      rows={6}
                      className="w-full bg-gray-800 text-gray-200 rounded-md py-2 px-3 font-mono text-sm focus:ring-2 focus:ring-blue-500 focus:outline-none resize-y"
                    />
                  </div>

                  <div className="flex items-center space-x-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={command.enabled}
                        onChange={(e) => updateCommand(index, 'enabled', e.target.checked)}
                        className="form-checkbox text-blue-600 bg-gray-800"
                      />
                      <span className="text-gray-300">Enabled</span>
                    </label>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Events Section */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">Events</h2>
            <button 
              onClick={addEvent}
              className="flex items-center px-3 py-2 bg-blue-700 text-white rounded hover:bg-blue-600 transition"
            >
              <FiPlus className="mr-2" /> Add Event
            </button>
          </div>

          {config.events.length === 0 ? (
            <div className="bg-gray-700 rounded-lg p-4 text-center text-gray-400">
              No events yet. Add event handlers to respond to Discord events!
            </div>
          ) : (
            <div className="space-y-4">
              {config.events.map((event, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-white">
                      {event.name || `Event ${index + 1}`}
                    </h3>
                    <button
                      onClick={() => removeEvent(index)}
                      className="text-red-500 hover:text-red-400 transition"
                    >
                      <FiTrash2 />
                    </button>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Event Name
                      </label>
                      <select
                        value={event.name}
                        onChange={(e) => updateEvent(index, 'name', e.target.value)}
                        className="w-full bg-gray-800 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                      >
                        <option value="">Select an event</option>
                        {eventTypes.map(type => (
                          <option key={type} value={type}>{type}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Trigger Type
                      </label>
                      <select
                        value={event.once ? 'once' : 'recurring'}
                        onChange={(e) => updateEvent(index, 'once', e.target.value === 'once')}
                        className="w-full bg-gray-800 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                      >
                        <option value="once">Once</option>
                        <option value="recurring">Recurring</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Event Code
                    </label>
                    <textarea
                      value={event.code}
                      onChange={(e) => updateEvent(index, 'code', e.target.value)}
                      placeholder="// Your event handler code here"
                      rows={6}
                      className="w-full bg-gray-800 text-gray-200 rounded-md py-2 px-3 font-mono text-sm focus:ring-2 focus:ring-blue-500 focus:outline-none resize-y"
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4">
          <button
            onClick={handlePreview}
            className="flex items-center px-4 py-2 bg-blue-700 text-white rounded hover:bg-blue-600 transition"
          >
            <FiEye className="mr-2" /> Preview
          </button>
          <button
            onClick={handleCreate}
            disabled={isCreating}
            className={`flex items-center px-4 py-2 rounded transition ${
              isCreating 
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                : 'bg-green-700 text-white hover:bg-green-600'
            }`}
          >
            <FiRocket className="mr-2" /> 
            {isCreating ? 'Creating...' : 'Create Addon'}
          </button>
        </div>

        {/* Preview Modal (to be implemented) */}
        {previewCode && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full">
              <h2 className="text-xl font-semibold text-white mb-4">Addon Preview</h2>
              <pre className="bg-gray-900 p-4 rounded text-gray-200 overflow-x-auto">
                {previewCode}
              </pre>
              <div className="flex justify-end mt-4">
                <button
                  onClick={() => setPreviewCode('')}
                  className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 