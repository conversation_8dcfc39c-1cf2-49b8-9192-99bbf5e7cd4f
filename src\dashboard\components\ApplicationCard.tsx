import React, { useState } from 'react';
import { 
  <PERSON><PERSON>lock, 
  FiUsers, 
  FiCheck, 
  FiAlertCircle, 
  FiCalendarAlt, 
  FiX 
} from 'react-icons/fi';
import { ApplicationType } from '../types/applications';
import { useTheme } from '../contexts/ThemeContext';

interface ApplicationCardProps {
  application: ApplicationType;
  onApply: (appType: ApplicationType) => void;
  hasApplied?: boolean;
}

export const ApplicationCard: React.FC<ApplicationCardProps> = ({
  application,
  onApply,
  hasApplied = false,
}) => {
  const { currentScheme } = useTheme();
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  
  // Map of possible icons
  const iconMap = {
    FiClock, FiUsers, FiCheck, FiAlertCircle, FiCalendarAlt, FiX
  };

  const IconComponent = application.icon && iconMap[application.icon as keyof typeof iconMap] || FiClock;

  // Tailwind dynamic class generation
  const getBackgroundStyle = () => {
    if (application.gradient) {
      return `bg-gradient-to-r from-[${application.gradient.from}] to-[${application.gradient.to}]`;
    }
    return 'bg-gray-800';
  };

  const getStatusStyle = () => {
    return application.isOpen 
      ? `border-${application.color}-400 hover:translate-y-[-2px] hover:shadow-[0_4px_12px_rgba(0,0,0,0.2)]`
      : 'border-gray-600 opacity-70';
  };

  const getApplyButtonStyle = () => {
    if (!application.isOpen || hasApplied) {
      return 'bg-gray-500 text-gray-300 cursor-not-allowed opacity-50';
    }
    return `bg-${application.color}-500 text-white hover:bg-${application.color}-600`;
  };

  return (
    <div 
      className={`
        ${getBackgroundStyle()}
        rounded-xl border p-6 relative transition-all duration-300
        ${getStatusStyle()}
        sm:max-w-md md:max-w-lg lg:max-w-xl
      `}
    >
      {/* Card Content */}
      <div className="flex flex-col space-y-4">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <IconComponent 
              className={`w-6 h-6 text-${application.color}-300`} 
            />
            <h2 className="text-xl font-bold text-white">
              {application.title}
            </h2>
          </div>
          <span 
            className={`
              px-3 py-1 rounded-full text-white
              ${application.isOpen ? 'bg-green-500' : 'bg-gray-500'}
            `}
          >
            {application.isOpen ? 'Open' : 'Closed'}
          </span>
        </div>

        {/* Description */}
        <p className="text-gray-300 line-clamp-2">
          {application.description}
        </p>

        {/* Metadata */}
        <div className="flex space-x-4 text-gray-400 text-sm">
          {[
            { icon: FiClock, text: application.metadata.averageResponseTime, title: 'Average response time' },
            { icon: FiUsers, text: application.metadata.totalApplications, title: 'Total applications' },
            { icon: FiCheck, text: `${application.metadata.acceptanceRate}%`, title: 'Acceptance rate' }
          ].map(({ icon: Icon, text, title }, index) => (
            <div 
              key={index}
              className="flex items-center space-x-1 group cursor-help"
              title={title}
            >
              <Icon />
              <span>{text}</span>
            </div>
          ))}
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center">
          <button
            onClick={() => setIsDetailsOpen(true)}
            className={`
              text-${application.color}-300 hover:bg-${application.color}-900 
              hover:text-${application.color}-200 px-2 py-1 rounded
            `}
          >
            View Details
          </button>
          <button
            disabled={!application.isOpen || hasApplied}
            onClick={() => onApply(application)}
            className={`
              ${getApplyButtonStyle()}
              px-4 py-2 rounded
            `}
          >
            {hasApplied ? 'Applied' : 'Apply Now'}
          </button>
        </div>
      </div>

      {/* Details Modal (similar structure, optimized for Tailwind) */}
      {isDetailsOpen && (
        <div 
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
          onClick={() => setIsDetailsOpen(false)}
        >
          {/* Modal content with similar Tailwind optimizations */}
          {/* ... (rest of the modal code remains similar) */}
        </div>
      )}
    </div>
  );
}; 