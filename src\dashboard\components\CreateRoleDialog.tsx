// @ts-nocheck
import React, { useState } from 'react';
import { 
  FiShield, 
  FiMessageSquare, 
  FiUsers, 
  FiVolume2 
} from 'react-icons/fi';

const PERMISSION_GROUPS = {
  General: {
    icon: FiShield,
    permissions: [
      'ADMINISTRATOR',
      'VIEW_AUDIT_LOG',
      '<PERSON><PERSON>GE_GUILD',
      'MANAGE_ROLES',
      'MANAGE_CHANNELS',
      'MANAGE_EMOJIS_AND_STICKERS',
      'MANAGE_WEBHOOKS',
      'VIEW_CHANNEL',
    ],
  },
  Text: {
    icon: FiMessageSquare,
    permissions: [
      'SEND_MESSAGES',
      'EMBED_LINKS',
      'ATTACH_FILES',
      'ADD_REACTIONS',
      'USE_EXTERNAL_EMOJIS',
      'MENTION_EVERYONE',
      'MANAGE_MESSAGES',
      'READ_MESSAGE_HISTORY',
    ],
  },
  Voice: {
    icon: FiVolume2,
    permissions: [
      'CONNECT',
      'SPEAK',
      'STREAM',
      'USE_VAD',
      'PRIORITY_SPEAKER',
      'MUTE_MEMBERS',
      'DEAFEN_MEMBERS',
      'MOVE_MEMBERS',
    ],
  },
  Members: {
    icon: FiUsers,
    permissions: [
      'KICK_MEMBERS',
      'BAN_MEMBERS',
      'CHANGE_NICKNAME',
      'MANAGE_NICKNAMES',
      'CREATE_INSTANT_INVITE',
    ],
  },
};

interface CreateRoleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function CreateRoleDialog({ 
  isOpen, 
  onClose, 
  onSuccess 
}: CreateRoleDialogProps) {
  const [roleName, setRoleName] = useState('');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  if (!isOpen) return null;

  const togglePermission = (permission: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permission) 
        ? prev.filter(p => p !== permission)
        : [...prev, permission]
    );
  };

  const handleSubmit = () => {
    // Implement role creation logic
    onSuccess();
    onClose();
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black bg-opacity-50"
    >
      <div 
        className="relative w-full max-w-4xl mx-auto my-8 bg-white rounded-lg shadow-2xl"
      >
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FiShield className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl font-bold text-gray-800">
              Create New Role
            </h2>
          </div>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            ✕
          </button>
        </div>

        {/* Modal Body */}
        <div className="p-6 space-y-6 max-h-[70vh] overflow-y-auto">
          {/* Role Name Input */}
          <div>
            <label 
              htmlFor="roleName" 
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Role Name
            </label>
            <input 
              id="roleName"
              type="text" 
              value={roleName}
              onChange={(e) => setRoleName(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200"
              placeholder="Enter role name"
            />
          </div>

          {/* Permission Groups */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Role Permissions
            </h3>
            {Object.entries(PERMISSION_GROUPS).map(([groupName, group]) => (
              <div key={groupName} className="mb-4">
                <div className="flex items-center mb-2 space-x-2">
                  <group.icon className="w-5 h-5 text-blue-500" />
                  <h4 className="text-md font-medium text-gray-700">
                    {groupName} Permissions
                  </h4>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {group.permissions.map((permission) => (
                    <label 
                      key={permission} 
                      className="flex items-center space-x-2 cursor-pointer"
                    >
                      <input 
                        type="checkbox" 
                        checked={selectedPermissions.includes(permission)}
                        onChange={() => togglePermission(permission)}
                        className="form-checkbox text-blue-500"
                      />
                      <span className="text-sm text-gray-600">
                        {permission.replace(/_/g, ' ').toLowerCase()}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Modal Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 space-x-3">
          <button 
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 rounded-md border border-gray-300"
          >
            Cancel
          </button>
          <button 
            onClick={handleSubmit}
            disabled={!roleName}
            className={`
              px-4 py-2 text-sm font-medium text-white rounded-md
              ${roleName 
                ? 'bg-blue-600 hover:bg-blue-700' 
                : 'bg-gray-400 cursor-not-allowed'}
            `}
          >
            Create Role
          </button>
        </div>
      </div>
    </div>
  );
} 