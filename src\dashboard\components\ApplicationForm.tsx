import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';

// Timezone data
const TIMEZONES = [
  { value: 'GMT-12:00', label: '(GMT-12:00) International Date Line West' },
  { value: 'GMT-11:00', label: '(GMT-11:00) Midway Island, Samoa' },
  { value: 'GMT-10:00', label: '(GMT-10:00) Hawaii' },
  { value: 'GMT-09:00', label: '(GMT-09:00) Alaska' },
  { value: 'GMT-08:00', label: '(GMT-08:00) Pacific Time (US & Canada)' },
  { value: 'GMT-07:00', label: '(GMT-07:00) Mountain Time (US & Canada)' },
  { value: 'GMT-06:00', label: '(GMT-06:00) Central Time (US & Canada)' },
  { value: 'GMT-05:00', label: '(GMT-05:00) Eastern Time (US & Canada)' },
  { value: 'GMT-04:00', label: '(GMT-04:00) Atlantic Time (Canada)' },
  { value: 'GMT-03:00', label: '(GMT-03:00) Buenos Aires, Georgetown' },
  { value: 'GMT-02:00', label: '(GMT-02:00) Mid-Atlantic' },
  { value: 'GMT-01:00', label: '(GMT-01:00) Azores, Cape Verde Islands' },
  { value: 'GMT+00:00', label: '(GMT+00:00) London, Dublin, Edinburgh' },
  { value: 'GMT+01:00', label: '(GMT+01:00) Paris, Amsterdam, Berlin' },
  { value: 'GMT+02:00', label: '(GMT+02:00) Athens, Istanbul, Helsinki' },
  { value: 'GMT+03:00', label: '(GMT+03:00) Moscow, Baghdad, Kuwait' },
  { value: 'GMT+04:00', label: '(GMT+04:00) Abu Dhabi, Dubai, Baku' },
  { value: 'GMT+05:00', label: '(GMT+05:00) Karachi, Tashkent' },
  { value: 'GMT+06:00', label: '(GMT+06:00) Dhaka, Almaty' },
  { value: 'GMT+07:00', label: '(GMT+07:00) Bangkok, Jakarta' },
  { value: 'GMT+08:00', label: '(GMT+08:00) Beijing, Singapore, Hong Kong' },
  { value: 'GMT+09:00', label: '(GMT+09:00) Tokyo, Seoul, Osaka' },
  { value: 'GMT+10:00', label: '(GMT+10:00) Sydney, Melbourne, Brisbane' },
  { value: 'GMT+11:00', label: '(GMT+11:00) Solomon Islands' },
  { value: 'GMT+12:00', label: '(GMT+12:00) Auckland, Wellington' }
];

interface FormState {
  age: string;
  hoursPerWeek: string;
  timezone: string;
  motivation: string;
}

interface ApplicationFormProps {
  session: any;
  onFormChange: (formData: FormState) => void;
  initialData?: FormState;
  motivationLabel?: string;
  motivationPlaceholder?: string;
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({ 
  session, 
  onFormChange, 
  initialData, 
  motivationLabel = "Why do you want to be a moderator?", 
  motivationPlaceholder = "Tell us about your motivation and what you can bring to the team..." 
}) => {
  const formRef = useRef<HTMLDivElement>(null);
  
  // Local state for form fields
  const [formState, setFormState] = useState<FormState>({
    age: initialData?.age || '',
    hoursPerWeek: initialData?.hoursPerWeek || '',
    timezone: initialData?.timezone || '',
    motivation: initialData?.motivation || ''
  });

  // Use a ref to track changes and avoid immediate updates
  const formStateRef = useRef(formState);
  formStateRef.current = formState;

  // Scroll to top when step changes
  useEffect(() => {
    if (formRef.current) {
      formRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // Notify parent of changes using useEffect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onFormChange(formStateRef.current);
    }, 100); // Small delay to batch updates

    return () => clearTimeout(timeoutId);
  }, [formState, onFormChange]);

  // Handle input changes efficiently
  const handleInputChange = useCallback((field: keyof FormState, value: string) => {
    setFormState(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  return (
    <div ref={formRef} className="space-y-6">
      {/* User Info Card */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="discord-username" className="block text-sm font-medium text-gray-300 mb-2">
              Discord Username
            </label>
            <input
              id="discord-username"
              type="text"
              value={session?.user?.name ?? ''}
              readOnly
              className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 cursor-not-allowed opacity-75"
            />
          </div>

          <div>
            <label htmlFor="discord-user-id" className="block text-sm font-medium text-gray-300 mb-2">
              Discord User ID
            </label>
            <input
              id="discord-user-id"
              type="text"
              value={(session?.user as any)?.id ?? ''}
              readOnly
              className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 cursor-not-allowed opacity-75"
            />
          </div>
        </div>
      </div>

      {/* Personal Details Card */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label htmlFor="age" className="block text-sm font-medium text-gray-300 mb-2">
              Age <span className="text-red-500">*</span>
            </label>
            <input
              id="age"
              type="number"
              value={formState.age}
              onChange={(e) => handleInputChange('age', e.target.value)}
              min={13}
              placeholder="Enter your age"
              className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              required
            />
          </div>

          <div>
            <label htmlFor="hours-per-week" className="block text-sm font-medium text-gray-300 mb-2">
              Hours per Week <span className="text-red-500">*</span>
            </label>
            <input
              id="hours-per-week"
              type="number"
              value={formState.hoursPerWeek}
              onChange={(e) => handleInputChange('hoursPerWeek', e.target.value)}
              min={1}
              placeholder="Hours available"
              className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              required
            />
          </div>

          <div>
            <label htmlFor="timezone" className="block text-sm font-medium text-gray-300 mb-2">
              Timezone <span className="text-red-500">*</span>
            </label>
            <select
              id="timezone"
              value={formState.timezone}
              onChange={(e) => handleInputChange('timezone', e.target.value)}
              className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              required
            >
              <option value="" className="bg-gray-800">Select timezone</option>
              {TIMEZONES.map((tz) => (
                <option 
                  key={tz.value} 
                  value={tz.value} 
                  className="bg-gray-800"
                >
                  {tz.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Motivation Card */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <div>
          <label htmlFor="motivation" className="block text-sm font-medium text-gray-300 mb-2">
            {motivationLabel} <span className="text-red-500">*</span>
          </label>
          <textarea
            id="motivation"
            value={formState.motivation}
            onChange={(e) => handleInputChange('motivation', e.target.value)}
            placeholder={motivationPlaceholder}
            rows={6}
            className="w-full bg-gray-700 text-gray-200 rounded-md py-2 px-3 focus:ring-2 focus:ring-blue-500 focus:outline-none resize-y"
            required
          />
        </div>
      </div>
    </div>
  );
};

export default React.memo(ApplicationForm); 